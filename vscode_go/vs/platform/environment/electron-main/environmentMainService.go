/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/

package electronmain

import (
	"os"
	"path/filepath"
	"sync"

	basecommon "github.com/yudaprama/kawai-agent/vscode_go/vs/base/common"
	ipcnode "github.com/yudaprama/kawai-agent/vscode_go/vs/base/parts/ipc/node"
	environmentcommon "github.com/yudaprama/kawai-agent/vscode_go/vs/platform/environment/common"
	environmentnode "github.com/yudaprama/kawai-agent/vscode_go/vs/platform/environment/node"
	instantiationcommon "github.com/yudaprama/kawai-agent/vscode_go/vs/platform/instantiation/common"
	productcommon "github.com/yudaprama/kawai-agent/vscode_go/vs/platform/product/common"
)

// IEnvironmentMainService is the service identifier for EnvironmentMainService
var IEnvironmentMainService = instantiationcommon.CreateDecorator[IEnvironmentMainServiceInterface]("environmentMainService")

// IEnvironmentMainServiceInterface extends INativeEnvironmentService for electron-main environments
type IEnvironmentMainServiceInterface interface {
	environmentcommon.INativeEnvironmentService

	// --- backup paths
	BackupHome() string

	// --- V8 code caching
	CodeCachePath() *string
	UseCodeCache() bool

	// --- IPC
	MainIPCHandle() string
	MainLockfile() string

	// --- config
	DisableUpdates() bool

	// TODO@deepak1556 TODO@bpasero temporary until a real fix lands upstream
	EnableRDPDisplayTracking() bool

	UnsetSnapExportedVariables()
	RestoreSnapExportedVariables()
}

// EnvironmentMainService extends NativeEnvironmentService for electron-main environments
type EnvironmentMainService struct {
	*environmentnode.NativeEnvironmentService
	snapEnv map[string]string
	mu      sync.Mutex

	// Memoized values
	backupHome                  *string
	backupHomeMux               sync.Once
	mainIPCHandle               *string
	mainIPCHandleMux            sync.Once
	mainLockfile                *string
	mainLockfileMux             sync.Once
	disableUpdates              *bool
	disableUpdatesMux           sync.Once
	crossOriginIsolated         *bool
	crossOriginIsolatedMux      sync.Once
	enableRDPDisplayTracking    *bool
	enableRDPDisplayTrackingMux sync.Once
	codeCachePath               *string
	codeCachePathMux            sync.Once
	useCodeCache                *bool
	useCodeCacheMux             sync.Once
}

// NewEnvironmentMainService creates a new EnvironmentMainService
func NewEnvironmentMainService(args environmentcommon.NativeParsedArgs, productService productcommon.IProductService) *EnvironmentMainService {
	nativeService := environmentnode.NewNativeEnvironmentService(args, productService)

	return &EnvironmentMainService{
		NativeEnvironmentService: nativeService,
		snapEnv:                  make(map[string]string),
	}
}

// ServiceBrand returns the service brand
func (e *EnvironmentMainService) ServiceBrand() interface{} {
	return "environmentMainService"
}

// BackupHome returns the backup home directory (memoized)
func (e *EnvironmentMainService) BackupHome() string {
	e.backupHomeMux.Do(func() {
		backupPath := filepath.Join(e.UserDataPath(), "Backups")
		e.backupHome = &backupPath
	})
	return *e.backupHome
}

// MainIPCHandle returns the main IPC handle (memoized)
func (e *EnvironmentMainService) MainIPCHandle() string {
	e.mainIPCHandleMux.Do(func() {
		// Get product service version - simplified for now
		version := "1.0.0" // In real implementation, get from product service
		handle := ipcnode.CreateStaticIPCHandle(e.UserDataPath(), "main", version)
		e.mainIPCHandle = &handle
	})
	return *e.mainIPCHandle
}

// MainLockfile returns the main lockfile path (memoized)
func (e *EnvironmentMainService) MainLockfile() string {
	e.mainLockfileMux.Do(func() {
		lockfile := filepath.Join(e.UserDataPath(), "code.lock")
		e.mainLockfile = &lockfile
	})
	return *e.mainLockfile
}

// DisableUpdates returns whether updates are disabled (memoized)
func (e *EnvironmentMainService) DisableUpdates() bool {
	e.disableUpdatesMux.Do(func() {
		args := e.GetArgs()
		if val, exists := args["disable-updates"]; exists && val != nil {
			if boolVal, ok := val.(bool); ok {
				e.disableUpdates = &boolVal
			} else {
				disabled := true
				e.disableUpdates = &disabled
			}
		} else {
			disabled := false
			e.disableUpdates = &disabled
		}
	})
	return *e.disableUpdates
}

// CrossOriginIsolated returns whether cross-origin isolation is enabled (memoized)
func (e *EnvironmentMainService) CrossOriginIsolated() bool {
	e.crossOriginIsolatedMux.Do(func() {
		args := e.GetArgs()
		if val, exists := args["enable-coi"]; exists && val != nil {
			if boolVal, ok := val.(bool); ok {
				e.crossOriginIsolated = &boolVal
			} else {
				enabled := true
				e.crossOriginIsolated = &enabled
			}
		} else {
			enabled := false
			e.crossOriginIsolated = &enabled
		}
	})
	return *e.crossOriginIsolated
}

// EnableRDPDisplayTracking returns whether RDP display tracking is enabled (memoized)
func (e *EnvironmentMainService) EnableRDPDisplayTracking() bool {
	e.enableRDPDisplayTrackingMux.Do(func() {
		args := e.GetArgs()
		if val, exists := args["enable-rdp-display-tracking"]; exists && val != nil {
			if boolVal, ok := val.(bool); ok {
				e.enableRDPDisplayTracking = &boolVal
			} else {
				enabled := true
				e.enableRDPDisplayTracking = &enabled
			}
		} else {
			enabled := false
			e.enableRDPDisplayTracking = &enabled
		}
	})
	return *e.enableRDPDisplayTracking
}

// CodeCachePath returns the code cache path (memoized)
func (e *EnvironmentMainService) CodeCachePath() *string {
	e.codeCachePathMux.Do(func() {
		if path := os.Getenv("VSCODE_CODE_CACHE_PATH"); path != "" {
			e.codeCachePath = &path
		} else {
			e.codeCachePath = nil
		}
	})
	return e.codeCachePath
}

// UseCodeCache returns whether code caching is enabled (memoized)
func (e *EnvironmentMainService) UseCodeCache() bool {
	e.useCodeCacheMux.Do(func() {
		cachePath := e.CodeCachePath()
		useCache := cachePath != nil
		e.useCodeCache = &useCache
	})
	return *e.useCodeCache
}

// UnsetSnapExportedVariables unsets snap-exported environment variables
func (e *EnvironmentMainService) UnsetSnapExportedVariables() {
	e.mu.Lock()
	defer e.mu.Unlock()

	if !basecommon.IsLinux {
		return
	}

	envVars := parseEnvironForSnap()
	for key, value := range envVars {
		if len(key) > 17 && key[len(key)-17:] == "_VSCODE_SNAP_ORIG" {
			originalKey := key[:len(key)-17] // Remove the _VSCODE_SNAP_ORIG suffix
			if _, exists := e.snapEnv[originalKey]; exists {
				continue
			}

			// Preserve the original value in case the snap env is re-entered
			if originalValue := os.Getenv(originalKey); originalValue != "" {
				e.snapEnv[originalKey] = originalValue
			}

			// Copy the original value from before entering the snap env if available,
			// if not delete the env variable.
			if value != "" {
				os.Setenv(originalKey, value)
			} else {
				os.Unsetenv(originalKey)
			}
		}
	}
}

// RestoreSnapExportedVariables restores snap-exported environment variables
func (e *EnvironmentMainService) RestoreSnapExportedVariables() {
	e.mu.Lock()
	defer e.mu.Unlock()

	if !basecommon.IsLinux {
		return
	}

	for key, value := range e.snapEnv {
		os.Setenv(key, value)
		delete(e.snapEnv, key)
	}
}

// Helper function to parse environment variables as key-value pairs
func parseEnviron() map[string]string {
	env := make(map[string]string)
	for _, pair := range os.Environ() {
		if idx := len(pair); idx > 0 {
			for i := 0; i < idx; i++ {
				if pair[i] == '=' {
					env[pair[:i]] = pair[i+1:]
					break
				}
			}
		}
	}
	return env
}

// Helper function to parse environment variables for snap handling
func parseEnvironForSnap() map[string]string {
	env := make(map[string]string)
	for _, pair := range os.Environ() {
		if len(pair) > 0 {
			for i := 0; i < len(pair); i++ {
				if pair[i] == '=' {
					key := pair[:i]
					value := pair[i+1:]
					env[key] = value
					break
				}
			}
		}
	}
	return env
}
