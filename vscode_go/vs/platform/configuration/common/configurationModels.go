/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/

package common

import (
	"encoding/json"
	"fmt"
	"regexp"
	"strings"
	"sync"

	basecommon "github.com/yudaprama/kawai-agent/vscode_go/vs/base/common"
	filescommon "github.com/yudaprama/kawai-agent/vscode_go/vs/platform/files/common"
	logcommon "github.com/yudaprama/kawai-agent/vscode_go/vs/platform/log/common"
	workspacecommon "github.com/yudaprama/kawai-agent/vscode_go/vs/platform/workspace/common"
)

// ConfigurationParseOptions represents options for parsing configuration
type ConfigurationParseOptions struct {
	SkipUnregistered *bool                `json:"skipUnregistered,omitempty"`
	Scopes           []ConfigurationScope `json:"scopes,omitempty"`
	SkipRestricted   *bool                `json:"skipRestricted,omitempty"`
	Include          []string             `json:"include,omitempty"`
	Exclude          []string             `json:"exclude,omitempty"`
}

// ConfigurationScope represents the scope of configuration
type ConfigurationScope int

const (
	ConfigurationScopeApplication ConfigurationScope = iota + 1
	ConfigurationScopeWindow
	ConfigurationScopeResource
	ConfigurationScopeLanguageOverridable
	ConfigurationScopeMachine
	ConfigurationScopeMachineOverridable
)

// InspectValue extends IInspectValue with merged field
type InspectValue[V any] struct {
	*IInspectValue[V]
	Merged *V `json:"merged,omitempty"`
}

// freeze simulates Object.freeze by returning a deep copy
func freeze[T any](data T) T {
	// In Go, we return the data as-is since Go doesn't have object freezing
	// In a real implementation, you might want to create immutable wrappers
	return data
}

// ConfigurationModel represents a configuration model
type ConfigurationModel struct {
	contents               interface{}
	keys                   []string
	overrides              []IOverrides
	raw                    interface{} // Can be IStringDictionary, slice, or ConfigurationModel
	logService             logcommon.ILogService
	overrideConfigurations map[string]*ConfigurationModel
	rawConfigurationCache  *ConfigurationModel
	mutex                  sync.RWMutex
}

// NewConfigurationModel creates a new configuration model
func NewConfigurationModel(
	contents interface{},
	keys []string,
	overrides []IOverrides,
	raw interface{},
	logService logcommon.ILogService,
) *ConfigurationModel {
	return &ConfigurationModel{
		contents:               contents,
		keys:                   keys,
		overrides:              overrides,
		raw:                    raw,
		logService:             logService,
		overrideConfigurations: make(map[string]*ConfigurationModel),
	}
}

// CreateEmptyModel creates an empty configuration model
func CreateEmptyModel(logService logcommon.ILogService) *ConfigurationModel {
	return NewConfigurationModel(
		make(map[string]interface{}),
		[]string{},
		[]IOverrides{},
		nil,
		logService,
	)
}

// GetContents returns the contents of the configuration model
func (cm *ConfigurationModel) GetContents() interface{} {
	return cm.contents
}

// GetKeys returns the keys of the configuration model
func (cm *ConfigurationModel) GetKeys() []string {
	return cm.keys
}

// GetOverrides returns the overrides of the configuration model
func (cm *ConfigurationModel) GetOverrides() []IOverrides {
	return cm.overrides
}

// GetRaw returns the raw configuration
func (cm *ConfigurationModel) GetRaw() interface{} {
	return cm.raw
}

// IsEmpty checks if the configuration model is empty
func (cm *ConfigurationModel) IsEmpty() bool {
	cm.mutex.RLock()
	defer cm.mutex.RUnlock()

	if len(cm.keys) > 0 {
		return false
	}

	if contentsMap, ok := cm.contents.(map[string]interface{}); ok {
		if len(contentsMap) > 0 {
			return false
		}
	}

	return len(cm.overrides) == 0
}

// GetValue gets a value from the configuration model
func (cm *ConfigurationModel) GetValue(section string) interface{} {
	if section == "" {
		return cm.contents
	}
	return GetConfigurationValue[interface{}](cm.contents.(map[string]interface{}), section, nil)
}

// Inspect inspects a configuration value
func (cm *ConfigurationModel) Inspect(section string, overrideIdentifier *string) *InspectValue[interface{}] {
	result := &InspectValue[interface{}]{
		IInspectValue: &IInspectValue[interface{}]{},
	}

	rawConfig := cm.GetRawConfiguration()

	// Set value
	value := rawConfig.GetValue(section)
	result.Value = &value

	// Set override if specified
	if overrideIdentifier != nil {
		override := rawConfig.GetOverrideValue(section, *overrideIdentifier)
		if override != nil {
			result.Override = &override
		}
	}

	// Set merged value
	if overrideIdentifier != nil {
		merged := rawConfig.Override(*overrideIdentifier).GetValue(section)
		result.Merged = &merged
	} else {
		merged := rawConfig.GetValue(section)
		result.Merged = &merged
	}

	// Set overrides array
	var overrideList []IInspectValueOverride[interface{}]
	for _, override := range rawConfig.GetOverrides() {
		tempModel := NewConfigurationModel(override.Contents, override.Keys, []IOverrides{}, nil, cm.logService)
		value := tempModel.GetValue(section)
		if value != nil {
			overrideList = append(overrideList, IInspectValueOverride[interface{}]{
				Identifiers: override.Identifiers,
				Value:       value,
			})
		}
	}
	if len(overrideList) > 0 {
		result.IInspectValue.Overrides = overrideList
	}

	return result
}

// GetRawConfiguration returns the raw configuration model
func (cm *ConfigurationModel) GetRawConfiguration() *ConfigurationModel {
	cm.mutex.Lock()
	defer cm.mutex.Unlock()

	if cm.rawConfigurationCache == nil {
		if cm.raw != nil {
			// Process raw configuration based on type
			var rawModels []*ConfigurationModel

			switch rawData := cm.raw.(type) {
			case []*ConfigurationModel:
				rawModels = rawData
			case []interface{}:
				for _, item := range rawData {
					if model, ok := item.(*ConfigurationModel); ok {
						rawModels = append(rawModels, model)
					} else {
						// Parse as raw configuration
						parser := NewConfigurationModelParser("", cm.logService)
						parser.ParseRaw(item, nil)
						rawModels = append(rawModels, parser.GetConfigurationModel())
					}
				}
			case map[string]interface{}:
				parser := NewConfigurationModelParser("", cm.logService)
				parser.ParseRaw(rawData, nil)
				rawModels = []*ConfigurationModel{parser.GetConfigurationModel()}
			case *ConfigurationModel:
				rawModels = []*ConfigurationModel{rawData}
			default:
				rawModels = []*ConfigurationModel{cm}
			}

			// Merge all raw models
			if len(rawModels) > 0 {
				result := rawModels[0]
				for i := 1; i < len(rawModels); i++ {
					if rawModels[i] != result {
						result = result.Merge(rawModels[i])
					}
				}
				cm.rawConfigurationCache = result
			} else {
				cm.rawConfigurationCache = cm
			}
		} else {
			cm.rawConfigurationCache = cm
		}
	}

	return cm.rawConfigurationCache
}

// GetOverrideValue gets an override value for a specific identifier
func (cm *ConfigurationModel) GetOverrideValue(section string, overrideIdentifier string) interface{} {
	overrideContents := cm.getContentsForOverrideIdentifier(overrideIdentifier)
	if overrideContents == nil {
		return nil
	}

	if section == "" {
		return overrideContents
	}

	return GetConfigurationValue[interface{}](overrideContents.(map[string]interface{}), section, nil)
}

// getContentsForOverrideIdentifier gets contents for a specific override identifier
func (cm *ConfigurationModel) getContentsForOverrideIdentifier(identifier string) interface{} {
	var contentsForIdentifierOnly map[string]interface{}
	var contents map[string]interface{}

	mergeContents := func(contentsToMerge interface{}) {
		if contentsToMerge == nil {
			return
		}

		if contentsMap, ok := contentsToMerge.(map[string]interface{}); ok {
			if contents == nil {
				contents = deepClone(contentsMap)
			} else {
				mergeObjects(contents, contentsMap)
			}
		}
	}

	for _, override := range cm.overrides {
		if len(override.Identifiers) == 1 && override.Identifiers[0] == identifier {
			if contentsMap, ok := interface{}(override.Contents).(map[string]interface{}); ok {
				contentsForIdentifierOnly = contentsMap
			}
		} else if contains(override.Identifiers, identifier) {
			mergeContents(interface{}(override.Contents))
		}
	}

	// Merge contents of the identifier only at the end to take precedence
	mergeContents(contentsForIdentifierOnly)

	return contents
}

// Helper functions
func contains(slice []string, item string) bool {
	for _, s := range slice {
		if s == item {
			return true
		}
	}
	return false
}

func deepClone(src map[string]interface{}) map[string]interface{} {
	dst := make(map[string]interface{})
	for k, v := range src {
		switch val := v.(type) {
		case map[string]interface{}:
			dst[k] = deepClone(val)
		default:
			dst[k] = val
		}
	}
	return dst
}

func mergeObjects(target, source map[string]interface{}) {
	for key, value := range source {
		if key == "__proto__" {
			continue
		}

		if targetValue, exists := target[key]; exists {
			if targetMap, ok := targetValue.(map[string]interface{}); ok {
				if sourceMap, ok := value.(map[string]interface{}); ok {
					mergeObjects(targetMap, sourceMap)
					continue
				}
			}
		}
		target[key] = deepCloneValue(value)
	}
}

func deepCloneValue(value interface{}) interface{} {
	switch val := value.(type) {
	case map[string]interface{}:
		return deepClone(val)
	case []interface{}:
		clone := make([]interface{}, len(val))
		for i, item := range val {
			clone[i] = deepCloneValue(item)
		}
		return clone
	default:
		return val
	}
}

// GetKeysForOverrideIdentifier gets keys for a specific override identifier
func (cm *ConfigurationModel) GetKeysForOverrideIdentifier(identifier string) []string {
	var keys []string
	keysSet := make(map[string]bool)

	for _, override := range cm.overrides {
		if contains(override.Identifiers, identifier) {
			for _, key := range override.Keys {
				if !keysSet[key] {
					keys = append(keys, key)
					keysSet[key] = true
				}
			}
		}
	}

	return keys
}

// GetAllOverrideIdentifiers gets all override identifiers
func (cm *ConfigurationModel) GetAllOverrideIdentifiers() []string {
	var identifiers []string
	identifierSet := make(map[string]bool)

	for _, override := range cm.overrides {
		for _, identifier := range override.Identifiers {
			if !identifierSet[identifier] {
				identifiers = append(identifiers, identifier)
				identifierSet[identifier] = true
			}
		}
	}

	return identifiers
}

// Override creates an override configuration model
func (cm *ConfigurationModel) Override(identifier string) *ConfigurationModel {
	cm.mutex.Lock()
	defer cm.mutex.Unlock()

	if overrideModel, exists := cm.overrideConfigurations[identifier]; exists {
		return overrideModel
	}

	overrideModel := cm.createOverrideConfigurationModel(identifier)
	cm.overrideConfigurations[identifier] = overrideModel
	return overrideModel
}

// createOverrideConfigurationModel creates an override configuration model
func (cm *ConfigurationModel) createOverrideConfigurationModel(identifier string) *ConfigurationModel {
	overrideContents := cm.getContentsForOverrideIdentifier(identifier)

	if overrideContents == nil {
		return cm
	}

	overrideMap, ok := overrideContents.(map[string]interface{})
	if !ok || len(overrideMap) == 0 {
		return cm
	}

	contents := make(map[string]interface{})
	contentsMap, _ := cm.contents.(map[string]interface{})

	// Merge base contents and override contents
	allKeys := make(map[string]bool)
	for key := range contentsMap {
		allKeys[key] = true
	}
	for key := range overrideMap {
		allKeys[key] = true
	}

	for key := range allKeys {
		contentsForKey := contentsMap[key]
		overrideContentsForKey := overrideMap[key]

		if overrideContentsForKey != nil {
			if baseMap, ok := contentsForKey.(map[string]interface{}); ok {
				if overrideMapForKey, ok := overrideContentsForKey.(map[string]interface{}); ok {
					// Clone and merge
					cloned := deepClone(baseMap)
					mergeObjects(cloned, overrideMapForKey)
					contentsForKey = cloned
				} else {
					contentsForKey = overrideContentsForKey
				}
			} else {
				contentsForKey = overrideContentsForKey
			}
		}

		contents[key] = contentsForKey
	}

	return NewConfigurationModel(contents, cm.keys, cm.overrides, nil, cm.logService)
}

// Merge merges multiple configuration models
func (cm *ConfigurationModel) Merge(others ...*ConfigurationModel) *ConfigurationModel {
	contents := deepClone(cm.contents.(map[string]interface{}))
	overrides := make([]IOverrides, len(cm.overrides))
	copy(overrides, cm.overrides)

	keys := make([]string, len(cm.keys))
	copy(keys, cm.keys)

	var raws []interface{}
	if cm.raw != nil {
		if rawSlice, ok := cm.raw.([]interface{}); ok {
			raws = append(raws, rawSlice...)
		} else {
			raws = append(raws, cm.raw)
		}
	} else {
		raws = append(raws, cm)
	}

	for _, other := range others {
		if other.raw != nil {
			if rawSlice, ok := other.raw.([]interface{}); ok {
				raws = append(raws, rawSlice...)
			} else {
				raws = append(raws, other.raw)
			}
		} else {
			raws = append(raws, other)
		}

		if other.IsEmpty() {
			continue
		}

		// Merge contents
		if otherContents, ok := other.contents.(map[string]interface{}); ok {
			mergeObjects(contents, otherContents)
		}

		// Merge overrides
		for _, otherOverride := range other.overrides {
			var found bool
			for i, override := range overrides {
				if slicesEqual(override.Identifiers, otherOverride.Identifiers) {
					// Merge this override
					if overrideContents, ok := interface{}(override.Contents).(map[string]interface{}); ok {
						if otherContents, ok := interface{}(otherOverride.Contents).(map[string]interface{}); ok {
							mergeObjects(overrideContents, otherContents)
						}
					}

					// Merge keys
					keySet := make(map[string]bool)
					for _, key := range override.Keys {
						keySet[key] = true
					}
					for _, key := range otherOverride.Keys {
						if !keySet[key] {
							override.Keys = append(override.Keys, key)
							keySet[key] = true
						}
					}
					overrides[i] = override
					found = true
					break
				}
			}

			if !found {
				clonedOverride := IOverrides{
					Identifiers: make([]string, len(otherOverride.Identifiers)),
					Keys:        make([]string, len(otherOverride.Keys)),
					Contents:    deepCloneValue(otherOverride.Contents).(map[string]interface{}),
				}
				copy(clonedOverride.Identifiers, otherOverride.Identifiers)
				copy(clonedOverride.Keys, otherOverride.Keys)
				overrides = append(overrides, clonedOverride)
			}
		}

		// Merge keys
		keySet := make(map[string]bool)
		for _, key := range keys {
			keySet[key] = true
		}
		for _, key := range other.keys {
			if !keySet[key] {
				keys = append(keys, key)
				keySet[key] = true
			}
		}
	}

	var finalRaw interface{}
	if len(raws) == 0 {
		finalRaw = nil
	} else {
		// Check if all raws are ConfigurationModel instances
		allModels := true
		for _, raw := range raws {
			if _, ok := raw.(*ConfigurationModel); !ok {
				allModels = false
				break
			}
		}
		if allModels {
			finalRaw = nil
		} else {
			finalRaw = raws
		}
	}

	return NewConfigurationModel(contents, keys, overrides, finalRaw, cm.logService)
}

// ToJSON converts the configuration model to JSON representation
func (cm *ConfigurationModel) ToJSON() *IConfigurationModel {
	return &IConfigurationModel{
		Contents:  cm.contents.(map[string]interface{}),
		Keys:      cm.keys,
		Overrides: cm.overrides,
	}
}

// AddValue adds a value to the configuration model
func (cm *ConfigurationModel) AddValue(key string, value interface{}) {
	cm.updateValue(key, value, true)
}

// SetValue sets a value in the configuration model
func (cm *ConfigurationModel) SetValue(key string, value interface{}) {
	cm.updateValue(key, value, false)
}

// RemoveValue removes a value from the configuration model
func (cm *ConfigurationModel) RemoveValue(key string) {
	keyIndex := -1
	for i, k := range cm.keys {
		if k == key {
			keyIndex = i
			break
		}
	}

	if keyIndex == -1 {
		return
	}

	// Remove key from slice
	cm.keys = append(cm.keys[:keyIndex], cm.keys[keyIndex+1:]...)

	// Remove from value tree
	RemoveFromValueTree(cm.contents.(map[string]interface{}), key)

	// Handle override property
	overrideRegex := regexp.MustCompile(`^\[.*\]$`)
	if overrideRegex.MatchString(key) {
		identifiers := overrideIdentifiersFromKey(key)
		for i, override := range cm.overrides {
			if slicesEqual(override.Identifiers, identifiers) {
				cm.overrides = append(cm.overrides[:i], cm.overrides[i+1:]...)
				break
			}
		}
	}
}

// updateValue updates a value in the configuration model
func (cm *ConfigurationModel) updateValue(key string, value interface{}, add bool) {
	AddToValueTree(cm.contents.(map[string]interface{}), key, value, func(msg string) {
		if cm.logService != nil {
			cm.logService.Error(msg)
		}
	})

	if add {
		keyExists := false
		for _, k := range cm.keys {
			if k == key {
				keyExists = true
				break
			}
		}
		if !keyExists {
			cm.keys = append(cm.keys, key)
		}
	}

	// Handle override property
	overrideRegex := regexp.MustCompile(`^\[.*\]$`)
	if overrideRegex.MatchString(key) {
		identifiers := overrideIdentifiersFromKey(key)

		if contentsMap, ok := cm.contents.(map[string]interface{}); ok {
			if keyContents, exists := contentsMap[key]; exists {
				if keyMap, ok := keyContents.(map[string]interface{}); ok {
					override := IOverrides{
						Identifiers: identifiers,
						Keys:        getMapKeys(keyMap),
						Contents: ToValuesTree(keyMap, func(msg string) {
							if cm.logService != nil {
								cm.logService.Error(msg)
							}
						}),
					}

					// Find and update existing override or add new one
					found := false
					for i, existingOverride := range cm.overrides {
						if slicesEqual(existingOverride.Identifiers, identifiers) {
							cm.overrides[i] = override
							found = true
							break
						}
					}

					if !found {
						cm.overrides = append(cm.overrides, override)
					}
				}
			}
		}
	}
}

// Helper functions
func slicesEqual(a, b []string) bool {
	if len(a) != len(b) {
		return false
	}
	for i, v := range a {
		if v != b[i] {
			return false
		}
	}
	return true
}

func getMapKeys(m map[string]interface{}) []string {
	keys := make([]string, 0, len(m))
	for k := range m {
		keys = append(keys, k)
	}
	return keys
}

func overrideIdentifiersFromKey(key string) []string {
	// Extract identifiers from override key format like "[lang]" or "[lang][os]"
	re := regexp.MustCompile(`\[([^\]]+)\]`)
	matches := re.FindAllStringSubmatch(key, -1)

	var identifiers []string
	for _, match := range matches {
		if len(match) > 1 {
			identifiers = append(identifiers, match[1])
		}
	}

	return identifiers
}

// ConfigurationModelParser parses configuration content
type ConfigurationModelParser struct {
	name                     string
	logService               logcommon.ILogService
	raw                      interface{}
	configurationModel       *ConfigurationModel
	restrictedConfigurations []string
	parseErrors              []interface{}
	mutex                    sync.RWMutex
}

// NewConfigurationModelParser creates a new configuration model parser
func NewConfigurationModelParser(name string, logService logcommon.ILogService) *ConfigurationModelParser {
	return &ConfigurationModelParser{
		name:       name,
		logService: logService,
	}
}

// GetConfigurationModel returns the parsed configuration model
func (cmp *ConfigurationModelParser) GetConfigurationModel() *ConfigurationModel {
	cmp.mutex.RLock()
	defer cmp.mutex.RUnlock()

	if cmp.configurationModel != nil {
		return cmp.configurationModel
	}
	return CreateEmptyModel(cmp.logService)
}

// GetRestrictedConfigurations returns the restricted configurations
func (cmp *ConfigurationModelParser) GetRestrictedConfigurations() []string {
	cmp.mutex.RLock()
	defer cmp.mutex.RUnlock()
	return cmp.restrictedConfigurations
}

// GetErrors returns parsing errors
func (cmp *ConfigurationModelParser) GetErrors() []interface{} {
	cmp.mutex.RLock()
	defer cmp.mutex.RUnlock()
	return cmp.parseErrors
}

// Parse parses configuration content
func (cmp *ConfigurationModelParser) Parse(content string, options *ConfigurationParseOptions) {
	if content != "" {
		raw := cmp.doParseContent(content)
		cmp.ParseRaw(raw, options)
	}
}

// Reparse reparses with new options
func (cmp *ConfigurationModelParser) Reparse(options *ConfigurationParseOptions) {
	if cmp.raw != nil {
		cmp.ParseRaw(cmp.raw, options)
	}
}

// ParseRaw parses raw configuration data
func (cmp *ConfigurationModelParser) ParseRaw(raw interface{}, options *ConfigurationParseOptions) {
	cmp.mutex.Lock()
	defer cmp.mutex.Unlock()

	cmp.raw = raw
	result := cmp.doParseRaw(raw, options)

	var finalRaw interface{}
	if result.HasExcludedProperties {
		finalRaw = raw
	}

	cmp.configurationModel = NewConfigurationModel(
		result.Contents,
		result.Keys,
		result.Overrides,
		finalRaw,
		cmp.logService,
	)
	cmp.restrictedConfigurations = result.Restricted
}

// doParseContent parses string content into raw data
func (cmp *ConfigurationModelParser) doParseContent(content string) interface{} {
	var raw interface{}

	if err := json.Unmarshal([]byte(content), &raw); err != nil {
		if cmp.logService != nil {
			cmp.logService.Error(fmt.Sprintf("Error while parsing settings file %s: %v", cmp.name, err))
		}
		cmp.parseErrors = append(cmp.parseErrors, err)
		return make(map[string]interface{})
	}

	return raw
}

// ParseResult represents the result of parsing configuration
type ParseResult struct {
	Contents              interface{}
	Keys                  []string
	Overrides             []IOverrides
	Restricted            []string
	HasExcludedProperties bool
}

// doParseRaw parses raw configuration data
func (cmp *ConfigurationModelParser) doParseRaw(raw interface{}, options *ConfigurationParseOptions) *ParseResult {
	// For now, implement a simplified version
	// In a full implementation, this would integrate with the configuration registry

	filtered := cmp.filter(raw, nil, true, options)

	contents := ToValuesTree(filtered.Raw.(map[string]interface{}), func(message string) {
		if cmp.logService != nil {
			cmp.logService.Error(fmt.Sprintf("Conflict in settings file %s: %s", cmp.name, message))
		}
	})

	keys := getMapKeys(filtered.Raw.(map[string]interface{}))
	overrides := cmp.toOverrides(filtered.Raw, func(message string) {
		if cmp.logService != nil {
			cmp.logService.Error(fmt.Sprintf("Conflict in settings file %s: %s", cmp.name, message))
		}
	})

	return &ParseResult{
		Contents:              contents,
		Keys:                  keys,
		Overrides:             overrides,
		Restricted:            filtered.Restricted,
		HasExcludedProperties: filtered.HasExcludedProperties,
	}
}

// FilterResult represents the result of filtering
type FilterResult struct {
	Raw                   interface{}
	Restricted            []string
	HasExcludedProperties bool
}

// filter filters configuration properties
func (cmp *ConfigurationModelParser) filter(properties interface{}, configurationProperties map[string]interface{}, filterOverriddenProperties bool, options *ConfigurationParseOptions) *FilterResult {
	if options == nil || (options.Scopes == nil && options.SkipRestricted == nil && len(options.Exclude) == 0) {
		if propertiesMap, ok := properties.(map[string]interface{}); ok {
			return &FilterResult{
				Raw:                   propertiesMap,
				Restricted:            []string{},
				HasExcludedProperties: false,
			}
		}
	}

	// Simplified filtering implementation
	raw := make(map[string]interface{})
	var restricted []string
	hasExcludedProperties := false

	if propertiesMap, ok := properties.(map[string]interface{}); ok {
		for key, value := range propertiesMap {
			// Simple exclusion check
			if options != nil && contains(options.Exclude, key) {
				hasExcludedProperties = true
				continue
			}

			raw[key] = value
		}
	}

	return &FilterResult{
		Raw:                   raw,
		Restricted:            restricted,
		HasExcludedProperties: hasExcludedProperties,
	}
}

// toOverrides converts raw data to overrides
func (cmp *ConfigurationModelParser) toOverrides(raw interface{}, conflictReporter func(string)) []IOverrides {
	var overrides []IOverrides

	if rawMap, ok := raw.(map[string]interface{}); ok {
		overrideRegex := regexp.MustCompile(`^\[.*\]$`)

		for key, value := range rawMap {
			if overrideRegex.MatchString(key) {
				if valueMap, ok := value.(map[string]interface{}); ok {
					overrideRaw := make(map[string]interface{})
					for k, v := range valueMap {
						overrideRaw[k] = v
					}

					override := IOverrides{
						Identifiers: overrideIdentifiersFromKey(key),
						Keys:        getMapKeys(overrideRaw),
						Contents:    ToValuesTree(overrideRaw, conflictReporter),
					}
					overrides = append(overrides, override)
				}
			}
		}
	}

	return overrides
}

// UserSettings handles user settings file
type UserSettings struct {
	userSettingsResource basecommon.URI
	parseOptions         *ConfigurationParseOptions
	fileService          filescommon.IFileService
	logService           logcommon.ILogService
	parser               *ConfigurationModelParser
	onDidChange          *basecommon.Emitter[interface{}]
	disposables          []basecommon.IDisposable
}

// NewUserSettings creates a new user settings instance
func NewUserSettings(
	userSettingsResource basecommon.URI,
	parseOptions *ConfigurationParseOptions,
	extUri basecommon.IExtUri,
	fileService filescommon.IFileService,
	logService logcommon.ILogService,
) *UserSettings {
	us := &UserSettings{
		userSettingsResource: userSettingsResource,
		parseOptions:         parseOptions,
		fileService:          fileService,
		logService:           logService,
		parser:               NewConfigurationModelParser(userSettingsResource.ToString(), logService),
		onDidChange:          basecommon.NewEmitter[interface{}](),
	}

	// Watch file changes (simplified implementation)
	// In a real implementation, this would set up file system watchers

	return us
}

// LoadConfiguration loads the configuration from file
func (us *UserSettings) LoadConfiguration() (*ConfigurationModel, error) {
	content, err := us.fileService.ReadFile(&us.userSettingsResource, nil, nil)
	if err != nil {
		return CreateEmptyModel(us.logService), nil
	}

	contentStr := content.Value.ToString()
	if contentStr == "" {
		contentStr = "{}"
	}

	us.parser.Parse(contentStr, us.parseOptions)
	return us.parser.GetConfigurationModel(), nil
}

// Reparse reparses the configuration with new options
func (us *UserSettings) Reparse(parseOptions *ConfigurationParseOptions) *ConfigurationModel {
	if parseOptions != nil {
		us.parseOptions = parseOptions
	}
	us.parser.Reparse(us.parseOptions)
	return us.parser.GetConfigurationModel()
}

// GetRestrictedSettings returns restricted settings
func (us *UserSettings) GetRestrictedSettings() []string {
	return us.parser.GetRestrictedConfigurations()
}

// OnDidChange returns the change event
func (us *UserSettings) OnDidChange() basecommon.Event[interface{}] {
	return us.onDidChange.Event()
}

// Dispose disposes the user settings
func (us *UserSettings) Dispose() {
	for _, disposable := range us.disposables {
		disposable.Dispose()
	}
}

// ConfigurationInspectValue represents a configuration inspect value
type ConfigurationInspectValue[V any] struct {
	key                      string
	overrides                *IConfigurationOverrides
	value                    *V
	overrideIdentifiers      []string
	defaultConfiguration     *ConfigurationModel
	policyConfiguration      *ConfigurationModel
	applicationConfiguration *ConfigurationModel
	userConfiguration        *ConfigurationModel
	localUserConfiguration   *ConfigurationModel
	remoteUserConfiguration  *ConfigurationModel
	workspaceConfiguration   *ConfigurationModel
	folderConfigurationModel *ConfigurationModel
	memoryConfigurationModel *ConfigurationModel

	// Cached values
	defaultInspectValue         *InspectValue[V]
	policyInspectValue          *InspectValue[V]
	applicationInspectValue     *InspectValue[V]
	userInspectValue            *InspectValue[V]
	userLocalInspectValue       *InspectValue[V]
	userRemoteInspectValue      *InspectValue[V]
	workspaceInspectValue       *InspectValue[V]
	workspaceFolderInspectValue *InspectValue[V]
	memoryInspectValue          *InspectValue[V]
}

// NewConfigurationInspectValue creates a new configuration inspect value
func NewConfigurationInspectValue[V any](
	key string,
	overrides *IConfigurationOverrides,
	value *V,
	overrideIdentifiers []string,
	defaultConfiguration *ConfigurationModel,
	policyConfiguration *ConfigurationModel,
	applicationConfiguration *ConfigurationModel,
	userConfiguration *ConfigurationModel,
	localUserConfiguration *ConfigurationModel,
	remoteUserConfiguration *ConfigurationModel,
	workspaceConfiguration *ConfigurationModel,
	folderConfigurationModel *ConfigurationModel,
	memoryConfigurationModel *ConfigurationModel,
) *ConfigurationInspectValue[V] {
	return &ConfigurationInspectValue[V]{
		key:                      key,
		overrides:                overrides,
		value:                    value,
		overrideIdentifiers:      overrideIdentifiers,
		defaultConfiguration:     defaultConfiguration,
		policyConfiguration:      policyConfiguration,
		applicationConfiguration: applicationConfiguration,
		userConfiguration:        userConfiguration,
		localUserConfiguration:   localUserConfiguration,
		remoteUserConfiguration:  remoteUserConfiguration,
		workspaceConfiguration:   workspaceConfiguration,
		folderConfigurationModel: folderConfigurationModel,
		memoryConfigurationModel: memoryConfigurationModel,
	}
}

// GetValue returns the frozen value
func (civ *ConfigurationInspectValue[V]) GetValue() *V {
	return civ.value
}

// GetDefaultValue returns the default value
func (civ *ConfigurationInspectValue[V]) GetDefaultValue() *V {
	inspect := civ.getDefaultInspectValue()
	return inspect.Merged
}

// GetPolicyValue returns the policy value
func (civ *ConfigurationInspectValue[V]) GetPolicyValue() *V {
	inspect := civ.getPolicyInspectValue()
	if inspect != nil {
		return inspect.Merged
	}
	return nil
}

// getDefaultInspectValue gets the default inspect value
func (civ *ConfigurationInspectValue[V]) getDefaultInspectValue() *InspectValue[V] {
	if civ.defaultInspectValue == nil {
		// For now, return a simplified version due to type conversion complexity
		civ.defaultInspectValue = &InspectValue[V]{
			IInspectValue: &IInspectValue[V]{},
		}
	}
	return civ.defaultInspectValue
}

// getPolicyInspectValue gets the policy inspect value
func (civ *ConfigurationInspectValue[V]) getPolicyInspectValue() *InspectValue[V] {
	if civ.policyInspectValue == nil && civ.policyConfiguration != nil {
		// For now, return a simplified version due to type conversion complexity
		civ.policyInspectValue = &InspectValue[V]{
			IInspectValue: &IInspectValue[V]{},
		}
	}
	return civ.policyInspectValue
}

// Configuration represents the main configuration manager
type Configuration struct {
	defaultConfiguration          *ConfigurationModel
	policyConfiguration           *ConfigurationModel
	applicationConfiguration      *ConfigurationModel
	localUserConfiguration        *ConfigurationModel
	remoteUserConfiguration       *ConfigurationModel
	workspaceConfiguration        *ConfigurationModel
	folderConfigurations          *basecommon.ResourceMap[*ConfigurationModel]
	memoryConfiguration           *ConfigurationModel
	memoryConfigurationByResource *basecommon.ResourceMap[*ConfigurationModel]
	logService                    logcommon.ILogService

	// Cached configurations
	workspaceConsolidatedConfiguration *ConfigurationModel
	foldersConsolidatedConfigurations  *basecommon.ResourceMap[*ConfigurationModel]
	userConfigurationCache             *ConfigurationModel

	mutex sync.RWMutex
}

// NewConfiguration creates a new configuration manager
func NewConfiguration(
	defaultConfiguration *ConfigurationModel,
	policyConfiguration *ConfigurationModel,
	applicationConfiguration *ConfigurationModel,
	localUserConfiguration *ConfigurationModel,
	remoteUserConfiguration *ConfigurationModel,
	workspaceConfiguration *ConfigurationModel,
	folderConfigurations *basecommon.ResourceMap[*ConfigurationModel],
	memoryConfiguration *ConfigurationModel,
	memoryConfigurationByResource *basecommon.ResourceMap[*ConfigurationModel],
	logService logcommon.ILogService,
) *Configuration {
	return &Configuration{
		defaultConfiguration:              defaultConfiguration,
		policyConfiguration:               policyConfiguration,
		applicationConfiguration:          applicationConfiguration,
		localUserConfiguration:            localUserConfiguration,
		remoteUserConfiguration:           remoteUserConfiguration,
		workspaceConfiguration:            workspaceConfiguration,
		folderConfigurations:              folderConfigurations,
		memoryConfiguration:               memoryConfiguration,
		memoryConfigurationByResource:     memoryConfigurationByResource,
		logService:                        logService,
		foldersConsolidatedConfigurations: basecommon.NewResourceMap[*ConfigurationModel](),
	}
}

// GetValue gets a configuration value
func (c *Configuration) GetValue(section string, overrides *IConfigurationOverrides, workspace *workspacecommon.Workspace) interface{} {
	consolidatedModel := c.getConsolidatedConfigurationModel(section, overrides, workspace)
	return consolidatedModel.GetValue(section)
}

// UpdateValue updates a configuration value
func (c *Configuration) UpdateValue(key string, value interface{}, overrides *IConfigurationUpdateOverrides) {
	if overrides == nil {
		overrides = &IConfigurationUpdateOverrides{}
	}

	var memoryConfiguration *ConfigurationModel
	if overrides.Resource != nil {
		memoryConfiguration, exists := c.memoryConfigurationByResource.Get(overrides.Resource)
		if !exists {
			memoryConfiguration = CreateEmptyModel(c.logService)
			c.memoryConfigurationByResource.Set(overrides.Resource, memoryConfiguration)
		}
	} else {
		memoryConfiguration = c.memoryConfiguration
	}

	if value == nil {
		memoryConfiguration.RemoveValue(key)
	} else {
		memoryConfiguration.SetValue(key, value)
	}

	if overrides.Resource == nil {
		c.mutex.Lock()
		c.workspaceConsolidatedConfiguration = nil
		c.mutex.Unlock()
	}
}

// Inspect inspects a configuration value
func (c *Configuration) Inspect(key string, overrides *IConfigurationOverrides, workspace *workspacecommon.Workspace) *IConfigurationValue[interface{}] {
	consolidatedModel := c.getConsolidatedConfigurationModel(key, overrides, workspace)
	folderModel := c.getFolderConfigurationModelForResource(overrides.Resource, workspace)

	var memoryModel *ConfigurationModel
	var exists bool
	if overrides.Resource != nil {
		memoryModel, exists = c.memoryConfigurationByResource.Get(overrides.Resource)
		if !exists {
			memoryModel = c.memoryConfiguration
		}
	} else {
		memoryModel = c.memoryConfiguration
	}

	// Get override identifiers
	var overrideIdentifiers []string
	for _, override := range consolidatedModel.GetOverrides() {
		for _, identifier := range override.Identifiers {
			if consolidatedModel.GetOverrideValue(key, identifier) != nil {
				overrideIdentifiers = append(overrideIdentifiers, identifier)
			}
		}
	}

	value := consolidatedModel.GetValue(key)

	return &IConfigurationValue[interface{}]{
		Value:                value,
		DefaultValue:         *c.getValueFromModel(c.defaultConfiguration, key),
		UserValue:            *c.getValueFromModel(c.getUserConfiguration(), key),
		UserLocalValue:       *c.getValueFromModel(c.localUserConfiguration, key),
		UserRemoteValue:      *c.getValueFromModel(c.remoteUserConfiguration, key),
		WorkspaceValue:       *c.getValueFromModel(c.workspaceConfiguration, key),
		WorkspaceFolderValue: *c.getValueFromModel(folderModel, key),
		MemoryValue:          *c.getValueFromModel(memoryModel, key),
		PolicyValue:          c.getValueFromModel(c.policyConfiguration, key),
	}
}

// getValueFromModel gets value from a configuration model
func (c *Configuration) getValueFromModel(model *ConfigurationModel, key string) *interface{} {
	if model == nil || model.IsEmpty() {
		return nil
	}
	value := model.GetValue(key)
	return &value
}

// getUserConfiguration gets the consolidated user configuration
func (c *Configuration) getUserConfiguration() *ConfigurationModel {
	c.mutex.Lock()
	defer c.mutex.Unlock()

	if c.userConfigurationCache == nil {
		if c.remoteUserConfiguration.IsEmpty() {
			c.userConfigurationCache = c.localUserConfiguration
		} else {
			merged := c.localUserConfiguration.Merge(c.remoteUserConfiguration)
			c.userConfigurationCache = NewConfigurationModel(
				merged.GetContents(),
				merged.GetKeys(),
				merged.GetOverrides(),
				nil,
				c.logService,
			)
		}
	}
	return c.userConfigurationCache
}

// getConsolidatedConfigurationModel gets the consolidated configuration model
func (c *Configuration) getConsolidatedConfigurationModel(section string, overrides *IConfigurationOverrides, workspace *workspacecommon.Workspace) *ConfigurationModel {
	configModel := c.getConsolidatedConfigurationModelForResource(overrides, workspace)

	if overrides != nil && overrides.OverrideIdentifier != nil {
		configModel = configModel.Override(*overrides.OverrideIdentifier)
	}

	// Apply policy configuration if it has values for this section
	if !c.policyConfiguration.IsEmpty() && c.policyConfiguration.GetValue(section) != nil {
		// Clone by merging
		configModel = configModel.Merge()
		for _, key := range c.policyConfiguration.GetKeys() {
			configModel.SetValue(key, c.policyConfiguration.GetValue(key))
		}
	}

	return configModel
}

// getConsolidatedConfigurationModelForResource gets consolidated config for a resource
func (c *Configuration) getConsolidatedConfigurationModelForResource(overrides *IConfigurationOverrides, workspace *workspacecommon.Workspace) *ConfigurationModel {
	consolidatedConfig := c.getWorkspaceConsolidatedConfiguration()

	if workspace != nil && overrides != nil && overrides.Resource != nil {
		root := workspace.GetFolder(overrides.Resource)
		if root != nil {
			folderConfig := c.getFolderConsolidatedConfiguration(*root.URI)
			if folderConfig != nil {
				consolidatedConfig = folderConfig
			}
		}

		memoryConfigForResource, exists := c.memoryConfigurationByResource.Get(overrides.Resource)
		if exists {
			consolidatedConfig = consolidatedConfig.Merge(memoryConfigForResource)
		}
	}

	return consolidatedConfig
}

// getWorkspaceConsolidatedConfiguration gets the workspace consolidated configuration
func (c *Configuration) getWorkspaceConsolidatedConfiguration() *ConfigurationModel {
	c.mutex.Lock()
	defer c.mutex.Unlock()

	if c.workspaceConsolidatedConfiguration == nil {
		c.workspaceConsolidatedConfiguration = c.defaultConfiguration.Merge(
			c.applicationConfiguration,
			c.getUserConfiguration(),
			c.workspaceConfiguration,
			c.memoryConfiguration,
		)
	}
	return c.workspaceConsolidatedConfiguration
}

// getFolderConsolidatedConfiguration gets folder consolidated configuration
func (c *Configuration) getFolderConsolidatedConfiguration(folder basecommon.URI) *ConfigurationModel {
	c.mutex.Lock()
	defer c.mutex.Unlock()

	folderConsolidated, folderConsolidatedExists := c.foldersConsolidatedConfigurations.Get(&folder)
	if !folderConsolidatedExists {
		workspaceConsolidated := c.getWorkspaceConsolidatedConfiguration()
		folderConfig, folderConfigExists := c.folderConfigurations.Get(&folder)
		if folderConfigExists {
			folderConsolidated = workspaceConsolidated.Merge(folderConfig)
			c.foldersConsolidatedConfigurations.Set(&folder, folderConsolidated)
		} else {
			folderConsolidated = workspaceConsolidated
		}
	}
	return folderConsolidated
}

// getFolderConfigurationModelForResource gets folder config model for a resource
func (c *Configuration) getFolderConfigurationModelForResource(resource *basecommon.URI, workspace *workspacecommon.Workspace) *ConfigurationModel {
	if workspace != nil && resource != nil {
		root := workspace.GetFolder(resource)
		if root != nil {
			folderConfig, folderConfigExists := c.folderConfigurations.Get(root.URI)
			if folderConfigExists {
				return folderConfig
			}
		}
	}
	return nil
}

// ToData converts configuration to data representation
func (c *Configuration) ToData() *IConfigurationData {
	folders := make(map[string]IConfigurationModel)

	c.folderConfigurations.ForEach(func(uri *basecommon.URI, model *ConfigurationModel) {
		folders[uri.ToString()] = *model.ToJSON()
	})

	return &IConfigurationData{
		Defaults:  *c.defaultConfiguration.ToJSON(),
		User:      *c.localUserConfiguration.ToJSON(),
		Workspace: *c.workspaceConfiguration.ToJSON(),
		Folders:   folders,
	}
}

// ConfigurationChangeEvent represents a configuration change event
type ConfigurationChangeEvent struct {
	change   *IConfigurationChange
	previous *struct {
		Workspace *workspacecommon.Workspace
		Data      *IConfigurationData
	}
	currentConfiguration       *Configuration
	currentWorkspace           *workspacecommon.Workspace
	logService                 logcommon.ILogService
	affectedKeys               map[string]bool
	affectsConfigStr           string
	marker                     string
	markerCode1                rune
	markerCode2                rune
	source                     ConfigurationTarget
	previousConfigurationCache *Configuration
}

// NewConfigurationChangeEvent creates a new configuration change event
func NewConfigurationChangeEvent(
	change *IConfigurationChange,
	previous *struct {
		Workspace *workspacecommon.Workspace
		Data      *IConfigurationData
	},
	currentConfiguration *Configuration,
	currentWorkspace *workspacecommon.Workspace,
	logService logcommon.ILogService,
) *ConfigurationChangeEvent {
	event := &ConfigurationChangeEvent{
		change:               change,
		previous:             previous,
		currentConfiguration: currentConfiguration,
		currentWorkspace:     currentWorkspace,
		logService:           logService,
		affectedKeys:         make(map[string]bool),
		marker:               "\n",
		markerCode1:          '\n',
		markerCode2:          '.',
	}

	// Build affected keys
	for _, key := range change.Keys {
		event.affectedKeys[key] = true
	}
	for _, override := range change.Overrides {
		if keys, ok := override[1].([]string); ok {
			for _, key := range keys {
				event.affectedKeys[key] = true
			}
		}
	}

	// Build affects config string
	event.affectsConfigStr = event.marker
	for key := range event.affectedKeys {
		event.affectsConfigStr += key + event.marker
	}

	return event
}

// GetAffectedKeys returns the affected keys
func (ce *ConfigurationChangeEvent) GetAffectedKeys() map[string]bool {
	return ce.affectedKeys
}

// GetChange returns the configuration change
func (ce *ConfigurationChangeEvent) GetChange() *IConfigurationChange {
	return ce.change
}

// GetSource returns the configuration source
func (ce *ConfigurationChangeEvent) GetSource() ConfigurationTarget {
	return ce.source
}

// AffectsConfiguration checks if a configuration is affected
func (ce *ConfigurationChangeEvent) AffectsConfiguration(section string, overrides *IConfigurationOverrides) bool {
	needle := ce.marker + section
	index := strings.Index(ce.affectsConfigStr, needle)
	if index < 0 {
		return false
	}

	pos := index + len(needle)
	if pos >= len(ce.affectsConfigStr) {
		return false
	}

	code := rune(ce.affectsConfigStr[pos])
	if code != ce.markerCode1 && code != ce.markerCode2 {
		return false
	}

	if overrides != nil {
		prevConfig := ce.getPreviousConfiguration()
		var value1 interface{}
		if prevConfig != nil && ce.previous != nil {
			value1 = prevConfig.GetValue(section, overrides, ce.previous.Workspace)
		}
		value2 := ce.currentConfiguration.GetValue(section, overrides, ce.currentWorkspace)
		return !deepEqual(value1, value2)
	}

	return true
}

// getPreviousConfiguration gets the previous configuration
func (ce *ConfigurationChangeEvent) getPreviousConfiguration() *Configuration {
	if ce.previousConfigurationCache == nil && ce.previous != nil {
		ce.previousConfigurationCache = ParseConfiguration(ce.previous.Data, ce.logService)
	}
	return ce.previousConfigurationCache
}

// Utility functions

// ParseConfiguration parses configuration data
func ParseConfiguration(data *IConfigurationData, logService logcommon.ILogService) *Configuration {
	defaultConfig := parseConfigurationModel(&data.Defaults, logService)
	userConfig := parseConfigurationModel(&data.User, logService)
	workspaceConfig := parseConfigurationModel(&data.Workspace, logService)

	folders := basecommon.NewResourceMap[*ConfigurationModel]()
	for folderURI, folderData := range data.Folders {
		if uri := basecommon.ParseURI(folderURI); uri != nil {
			model := parseConfigurationModel(&folderData, logService)
			folders.Set(uri, model)
		}
	}

	return NewConfiguration(
		defaultConfig,
		CreateEmptyModel(logService), // policy
		CreateEmptyModel(logService), // application
		userConfig,                   // local user
		CreateEmptyModel(logService), // remote user
		workspaceConfig,
		folders,
		CreateEmptyModel(logService), // memory
		basecommon.NewResourceMap[*ConfigurationModel](), // memory by resource
		logService,
	)
}

// parseConfigurationModel parses a configuration model from data
func parseConfigurationModel(model *IConfigurationModel, logService logcommon.ILogService) *ConfigurationModel {
	return NewConfigurationModel(
		model.Contents,
		model.Keys,
		model.Overrides,
		nil, // raw data not available
		logService,
	)
}

// MergeChanges merges multiple configuration changes
func MergeChanges(changes ...*IConfigurationChange) *IConfigurationChange {
	if len(changes) == 0 {
		return &IConfigurationChange{
			Keys:      []string{},
			Overrides: [][2]interface{}{},
		}
	}
	if len(changes) == 1 {
		return changes[0]
	}

	keysSet := make(map[string]bool)
	overridesMap := make(map[string]map[string]bool)

	for _, change := range changes {
		for _, key := range change.Keys {
			keysSet[key] = true
		}
		for _, override := range change.Overrides {
			if len(override) >= 2 {
				if identifier, ok := override[0].(string); ok {
					if keys, ok := override[1].([]string); ok {
						if overridesMap[identifier] == nil {
							overridesMap[identifier] = make(map[string]bool)
						}
						for _, key := range keys {
							overridesMap[identifier][key] = true
						}
					}
				}
			}
		}
	}

	var keys []string
	for key := range keysSet {
		keys = append(keys, key)
	}

	var overrides [][2]interface{}
	for identifier, keySet := range overridesMap {
		var keyList []string
		for key := range keySet {
			keyList = append(keyList, key)
		}
		overrides = append(overrides, [2]interface{}{identifier, keyList})
	}

	return &IConfigurationChange{
		Keys:      keys,
		Overrides: overrides,
	}
}

// deepEqual compares two values for deep equality
func deepEqual(a, b interface{}) bool {
	// Simplified deep equality check
	// In a real implementation, this would be more comprehensive
	switch va := a.(type) {
	case map[string]interface{}:
		if vb, ok := b.(map[string]interface{}); ok {
			if len(va) != len(vb) {
				return false
			}
			for k, v := range va {
				if !deepEqual(v, vb[k]) {
					return false
				}
			}
			return true
		}
		return false
	case []interface{}:
		if vb, ok := b.([]interface{}); ok {
			if len(va) != len(vb) {
				return false
			}
			for i, v := range va {
				if !deepEqual(v, vb[i]) {
					return false
				}
			}
			return true
		}
		return false
	default:
		return a == b
	}
}
