/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/

package common

import (
	"fmt"
	"sync"

	basecommon "github.com/yudaprama/kawai-agent/vscode_go/vs/base/common"
	filescommon "github.com/yudaprama/kawai-agent/vscode_go/vs/platform/files/common"
	logcommon "github.com/yudaprama/kawai-agent/vscode_go/vs/platform/log/common"
	policycommon "github.com/yudaprama/kawai-agent/vscode_go/vs/platform/policy/common"
	workspacecommon "github.com/yudaprama/kawai-agent/vscode_go/vs/platform/workspace/common"
)

// ConfigurationService extends Disposable and implements IConfigurationService
type ConfigurationService struct {
	*basecommon.Disposable

	// Service brand for dependency injection
	serviceBrand interface{}

	// Configuration state
	configuration        *Configuration
	defaultConfiguration *DefaultConfiguration
	policyConfiguration  IPolicyConfiguration
	userConfiguration    *UserSettings

	// Event emitters
	onDidChangeConfigurationEmitter *basecommon.Emitter[IConfigurationChangeEvent]

	// Configuration editing
	configurationEditing *ConfigurationEditing

	// Dependencies
	settingsResource *basecommon.URI
	logService       logcommon.ILogService

	mu sync.RWMutex
}

// NewConfigurationService creates a new configuration service
func NewConfigurationService(
	settingsResource *basecommon.URI,
	fileService filescommon.IFileService,
	policyService policycommon.IPolicyService,
	logService logcommon.ILogService,
) *ConfigurationService {
	service := &ConfigurationService{
		Disposable:       basecommon.NewDisposable(),
		settingsResource: settingsResource,
		logService:       logService,
	}

	// Initialize configurations - simplified for now
	service.defaultConfiguration = &DefaultConfiguration{
		configurationModel: CreateEmptyModel(logService),
	}

	service.policyConfiguration = &NullPolicyConfiguration{
		configurationModel: CreateEmptyModel(logService),
	}

	service.userConfiguration = &UserSettings{
		userSettingsResource: *settingsResource,
		parser:               NewConfigurationModelParser("user", logService),
		parseOptions:         &ConfigurationParseOptions{},
		fileService:          fileService,
		logService:           logService,
	}

	// Initialize configuration
	service.configuration = NewConfiguration(
		service.defaultConfiguration.GetConfigurationModel(),
		service.policyConfiguration.GetConfigurationModel(),
		CreateEmptyModel(logService),
		CreateEmptyModel(logService),
		CreateEmptyModel(logService),
		CreateEmptyModel(logService),
		basecommon.NewResourceMap[*ConfigurationModel](),
		CreateEmptyModel(logService),
		basecommon.NewResourceMap[*ConfigurationModel](),
		logService,
	)

	// Initialize configuration editing
	service.configurationEditing = NewConfigurationEditing(settingsResource, fileService, service)

	// Initialize event emitters
	service.onDidChangeConfigurationEmitter = service.Register(basecommon.NewEmitter[IConfigurationChangeEvent]()).(*basecommon.Emitter[IConfigurationChangeEvent])

	// Register event listeners - simplified for now
	// TODO: Add proper event handling when the interfaces are fully implemented

	return service
}

// Initialize initializes the configuration service
func (cs *ConfigurationService) Initialize() error {
	// Load all configurations in parallel
	defaultModelChan := make(chan *ConfigurationModel, 1)
	policyModelChan := make(chan *ConfigurationModel, 1)
	userModelChan := make(chan *ConfigurationModel, 1)

	var wg sync.WaitGroup
	wg.Add(3)

	// Load default configuration
	go func() {
		defer wg.Done()
		model, err := cs.defaultConfiguration.Initialize()
		if err != nil {
			cs.logService.Error("Failed to initialize default configuration", err)
			defaultModelChan <- CreateEmptyModel(cs.logService)
		} else {
			defaultModelChan <- model
		}
	}()

	// Load policy configuration
	go func() {
		defer wg.Done()
		model, err := cs.policyConfiguration.Initialize()
		if err != nil {
			cs.logService.Error("Failed to initialize policy configuration", err)
			policyModelChan <- CreateEmptyModel(cs.logService)
		} else {
			policyModelChan <- model
		}
	}()

	// Load user configuration
	go func() {
		defer wg.Done()
		model, err := cs.userConfiguration.LoadConfiguration()
		if err != nil {
			cs.logService.Error("Failed to load user configuration", err)
			userModelChan <- CreateEmptyModel(cs.logService)
		} else {
			userModelChan <- model
		}
	}()

	wg.Wait()

	defaultModel := <-defaultModelChan
	policyModel := <-policyModelChan
	userModel := <-userModelChan

	cs.mu.Lock()
	cs.configuration = NewConfiguration(
		defaultModel,
		policyModel,
		CreateEmptyModel(cs.logService),
		userModel,
		CreateEmptyModel(cs.logService),
		CreateEmptyModel(cs.logService),
		basecommon.NewResourceMap[*ConfigurationModel](),
		CreateEmptyModel(cs.logService),
		basecommon.NewResourceMap[*ConfigurationModel](),
		cs.logService,
	)
	cs.mu.Unlock()

	return nil
}

// GetConfigurationData returns the configuration data
func (cs *ConfigurationService) GetConfigurationData() *IConfigurationData {
	cs.mu.RLock()
	defer cs.mu.RUnlock()
	return cs.configuration.ToData()
}

// GetValue gets a configuration value
func (cs *ConfigurationService) GetValue(arg1 interface{}, arg2 interface{}) interface{} {
	var section string
	var overrides *IConfigurationOverrides

	if arg1 != nil {
		if val, ok := arg1.(string); ok {
			section = val
		}
	}

	if arg2 != nil {
		if val, ok := arg2.(*IConfigurationOverrides); ok {
			overrides = val
		}
	}

	if overrides == nil {
		overrides = &IConfigurationOverrides{}
	}

	cs.mu.RLock()
	defer cs.mu.RUnlock()
	return cs.configuration.GetValue(section, overrides, nil)
}

// UpdateValue updates a configuration value
func (cs *ConfigurationService) UpdateValue(key string, value interface{}, args ...interface{}) error {
	var overrides *IConfigurationUpdateOverrides
	var target ConfigurationTarget

	// Parse arguments
	for _, arg := range args {
		switch v := arg.(type) {
		case *IConfigurationUpdateOverrides:
			overrides = v
		case *IConfigurationOverrides:
			// Convert to update overrides
			overrides = &IConfigurationUpdateOverrides{
				Resource: v.Resource,
				OverrideIdentifiers: func() []string {
					if v.OverrideIdentifier != nil && *v.OverrideIdentifier != "" {
						return []string{*v.OverrideIdentifier}
					}
					return nil
				}(),
			}
		case ConfigurationTarget:
			target = v
		}
	}

	// Validate target
	if target != UserLocal && target != User {
		return fmt.Errorf("unable to write %s to target %d", key, target)
	}

	// Handle override identifiers
	if overrides != nil && overrides.OverrideIdentifiers != nil {
		identifiers := basecommon.Unique(overrides.OverrideIdentifiers)
		if len(identifiers) == 0 {
			overrides.OverrideIdentifiers = nil
		} else {
			overrides.OverrideIdentifiers = identifiers
		}
	}

	// Check for policy restrictions
	inspectOverrides := &IConfigurationOverrides{}
	if overrides != nil {
		inspectOverrides.Resource = overrides.Resource
		if overrides.OverrideIdentifiers != nil && len(overrides.OverrideIdentifiers) > 0 {
			id := overrides.OverrideIdentifiers[0]
			inspectOverrides.OverrideIdentifier = &id
		}
	}

	inspect := cs.Inspect(key, *inspectOverrides)

	if inspect.PolicyValue != nil {
		return fmt.Errorf("unable to write %s because it is configured in system policy", key)
	}

	// Remove the setting if the value is same as default value
	if basecommon.Equals(value, inspect.DefaultValue) {
		value = nil
	}

	// Build path - simplified version
	var path basecommon.JSONPath
	if overrides != nil && overrides.OverrideIdentifiers != nil && len(overrides.OverrideIdentifiers) > 0 {
		path = basecommon.JSONPath{KeyFromOverrideIdentifiers(overrides.OverrideIdentifiers), key}
	} else {
		path = basecommon.JSONPath{key}
	}

	// Write configuration
	err := cs.configurationEditing.Write(path, value)
	if err != nil {
		return err
	}

	// Reload configuration
	return cs.ReloadConfiguration(nil)
}

// Inspect inspects a configuration value
func (cs *ConfigurationService) Inspect(key string, overrides IConfigurationOverrides) IConfigurationValue[interface{}] {
	cs.mu.RLock()
	defer cs.mu.RUnlock()
	return *cs.configuration.Inspect(key, &overrides, nil)
}

// Keys returns configuration keys
func (cs *ConfigurationService) Keys() map[string][]string {
	cs.mu.RLock()
	defer cs.mu.RUnlock()

	// Simplified implementation - return empty keys for now
	return map[string][]string{
		"default":         {},
		"user":            {},
		"workspace":       {},
		"workspaceFolder": {},
	}
}

// ReloadConfiguration reloads the configuration.
func (cs *ConfigurationService) ReloadConfiguration(target interface{}) error {
	// Simplified implementation - return nil for now
	return nil
}

// OnDidChangeConfiguration returns the configuration change event
func (cs *ConfigurationService) OnDidChangeConfiguration() basecommon.Event[IConfigurationChangeEvent] {
	return cs.onDidChangeConfigurationEmitter.Event()
}

// Private methods

func (cs *ConfigurationService) onDidChangeUserConfiguration(userConfigurationModel *ConfigurationModel) {
	cs.mu.Lock()
	previous := cs.configuration.ToData()

	// Simplified change handling - just update the configuration
	cs.configuration = NewConfiguration(
		cs.configuration.defaultConfiguration,
		cs.configuration.policyConfiguration,
		cs.configuration.applicationConfiguration,
		userConfigurationModel,
		cs.configuration.remoteUserConfiguration,
		cs.configuration.workspaceConfiguration,
		cs.configuration.folderConfigurations,
		cs.configuration.memoryConfiguration,
		cs.configuration.memoryConfigurationByResource,
		cs.logService,
	)
	cs.mu.Unlock()

	cs.trigger(nil, *previous, User)
}

func (cs *ConfigurationService) onDidDefaultConfigurationChange(defaultConfigurationModel *ConfigurationModel, properties []string) {
	cs.mu.Lock()
	previous := cs.configuration.ToData()

	// Simplified change handling - just update the configuration
	cs.configuration = NewConfiguration(
		defaultConfigurationModel,
		cs.configuration.policyConfiguration,
		cs.configuration.applicationConfiguration,
		cs.configuration.localUserConfiguration,
		cs.configuration.remoteUserConfiguration,
		cs.configuration.workspaceConfiguration,
		cs.configuration.folderConfigurations,
		cs.configuration.memoryConfiguration,
		cs.configuration.memoryConfigurationByResource,
		cs.logService,
	)
	cs.mu.Unlock()

	cs.trigger(nil, *previous, Default)
}

func (cs *ConfigurationService) onDidPolicyConfigurationChange(policyConfiguration *ConfigurationModel) {
	cs.mu.Lock()
	previous := cs.configuration.ToData()

	// Simplified change handling - just update the configuration
	cs.configuration = NewConfiguration(
		cs.configuration.defaultConfiguration,
		policyConfiguration,
		cs.configuration.applicationConfiguration,
		cs.configuration.localUserConfiguration,
		cs.configuration.remoteUserConfiguration,
		cs.configuration.workspaceConfiguration,
		cs.configuration.folderConfigurations,
		cs.configuration.memoryConfiguration,
		cs.configuration.memoryConfigurationByResource,
		cs.logService,
	)
	cs.mu.Unlock()

	cs.trigger(nil, *previous, Default)
}

func (cs *ConfigurationService) trigger(configurationChange *IConfigurationChange, previous IConfigurationData, source ConfigurationTarget) {
	// Create a simple change if none provided
	if configurationChange == nil {
		configurationChange = &IConfigurationChange{Keys: []string{}}
	}

	// Create and fire the event
	event := NewConfigurationChangeEvent(configurationChange, &struct { Workspace *workspacecommon.Workspace; Data *IConfigurationData }{nil, &previous}, cs.configuration, nil, cs.logService)
	event.source = source

	cs.onDidChangeConfigurationEmitter.Fire(event)
}

// ConfigurationEditing handles JSON file editing with queuing
type ConfigurationEditing struct {
	settingsResource     *basecommon.URI
	fileService          filescommon.IFileService
	configurationService IConfigurationService
	queue                *basecommon.Queue[interface{}]
	formattingOptions    *basecommon.FormattingOptions
}

// NewConfigurationEditing creates a new configuration editing instance
func NewConfigurationEditing(
	settingsResource *basecommon.URI,
	fileService filescommon.IFileService,
	configurationService IConfigurationService,
) *ConfigurationEditing {
	return &ConfigurationEditing{
		settingsResource:     settingsResource,
		fileService:          fileService,
		configurationService: configurationService,
		queue:                basecommon.NewQueue[interface{}](),
	}
}

// Write writes a configuration value to the settings file
func (ce *ConfigurationEditing) Write(path basecommon.JSONPath, value interface{}) error {
	resultChan := ce.queue.Queue(func() <-chan interface{} {
		result := make(chan interface{}, 1)
		go func() {
			defer close(result)
			err := ce.doWriteConfiguration(path, value)
			result <- err
		}()
		return result
	})

	// Wait for the result
	result := <-resultChan
	if err, ok := result.(error); ok {
		return err
	}
	return nil
}

// doWriteConfiguration performs the actual configuration writing
func (ce *ConfigurationEditing) doWriteConfiguration(path basecommon.JSONPath, value interface{}) error {
	var content string

	// Try to read existing file
	fileContent, err := ce.fileService.ReadFile(ce.settingsResource, nil, nil)
	if err != nil {
		if filescommon.ToFileOperationResult(err) == filescommon.FileOperationResultFileNotFound {
			content = "{}"
		} else {
			return err
		}
	} else {
		content = fileContent.Value.ToString()
	}

	// Parse JSON to check for errors
	errors := []basecommon.ParseError{}
	basecommon.ParseJSON(content, &errors, &basecommon.ParseOptions{
		AllowTrailingComma: true,
		AllowEmptyContent:  true,
	})

	if len(errors) > 0 {
		return fmt.Errorf("unable to write into the settings file. Please open the file to correct errors/warnings in the file and try again")
	}

	// Get edits
	edits := ce.getEdits(content, path, value)
	content = basecommon.ApplyEdits(content, edits)

	// Write file
	buffer := basecommon.VSBufferFromString(content)
	_, err = ce.fileService.WriteFile(ce.settingsResource, buffer, nil)
	return err
}

// getEdits generates edits for the configuration change
func (ce *ConfigurationEditing) getEdits(content string, path basecommon.JSONPath, value interface{}) []basecommon.Edit {
	options := ce.getFormattingOptions()

	// With empty path the entire file is being replaced
	if len(path) == 0 {
		var contentStr string
		if value != nil {
			if options.InsertSpaces != nil && *options.InsertSpaces {
				indent := ""
				if options.TabSize != nil {
					for i := 0; i < *options.TabSize; i++ {
						indent += " "
					}
				}
				// Simple JSON formatting
				contentStr = fmt.Sprintf("%v", value)
			} else {
				contentStr = fmt.Sprintf("%v", value)
			}
		} else {
			contentStr = ""
		}

		return []basecommon.Edit{{
			Offset:  0,
			Length:  len(content),
			Content: contentStr,
		}}
	}

	return basecommon.SetProperty(content, path, value, *options, nil)
}

// getFormattingOptions returns formatting options for JSON editing
func (ce *ConfigurationEditing) getFormattingOptions() *basecommon.FormattingOptions {
	if ce.formattingOptions != nil {
		return ce.formattingOptions
	}

	// Get EOL setting
	eol := "\n"
	if basecommon.IsWindows {
		eol = "\r\n"
	}

	// For now, use default values since we need to fix the GetValue signature first
	// TODO: Fix GetValue to support overrides properly

	// Get insert spaces setting
	insertSpaces := true

	// Get tab size setting
	tabSize := 4

	ce.formattingOptions = &basecommon.FormattingOptions{
		Eol:          &eol,
		InsertSpaces: &insertSpaces,
		TabSize:      &tabSize,
	}

	return ce.formattingOptions
}

// KeyFromOverrideIdentifiers creates a key from override identifiers
func KeyFromOverrideIdentifiers(overrideIdentifiers []string) string {
	result := ""
	for _, identifier := range overrideIdentifiers {
		result += "[" + identifier + "]"
	}
	return result
}
