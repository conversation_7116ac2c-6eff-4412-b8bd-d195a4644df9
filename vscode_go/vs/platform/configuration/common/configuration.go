package common

import (
	"strings"

	basecommon "github.com/yudaprama/kawai-agent/vscode_go/vs/base/common"
	instantiationcommon "github.com/yudaprama/kawai-agent/vscode_go/vs/platform/instantiation/common"
)

// IConfigurationService is the interface for the configuration service.
type IConfigurationService interface {
	GetValue(section string) interface{}
}

// IConfigurationOverrides represents configuration overrides.
type IConfigurationOverrides struct {
	OverrideIdentifier *string
	Resource           *basecommon.URI
}

// ConfigurationTarget represents the target for a configuration update.
type ConfigurationTarget int

const (
	// Application represents the application target.
	Application ConfigurationTarget = iota
	// User represents the user target.
	User
	// UserLocal represents the user local target.
	UserLocal
	// UserRemote represents the user remote target.
	UserRemote
	// Workspace represents the workspace target.
	Workspace
	// WorkspaceFolder represents the workspace folder target.
	WorkspaceFolder
	// Default represents the default target.
	Default
	// Memory represents the memory target.
	Memory
)

// IConfigurationChange represents a configuration change.
type IConfigurationChange struct {
	Keys      []string         `json:"keys"`
	Overrides [][2]interface{} `json:"overrides"`
}

// IConfigurationChangeEvent represents a configuration change event.
type IConfigurationChangeEvent struct {
	Source       ConfigurationTarget
	AffectedKeys []string
	Change       IConfigurationChange
}

// IInspectValue represents an inspected configuration value.
type IInspectValue[T any] struct {
	Value     T
	Override  T
	Overrides []IInspectValueOverride[T]
}

// IInspectValueOverride represents an override in an inspected value
type IInspectValueOverride[T any] struct {
	Identifiers []string `json:"identifiers"`
	Value       T        `json:"value"`
}

// IConfigurationValue represents a configuration value.
type IConfigurationValue[T any] struct {
	DefaultValue         T
	UserValue            T
	UserLocalValue       T
	UserRemoteValue      T
	WorkspaceValue       T
	WorkspaceFolderValue T
	MemoryValue          T
	PolicyValue          *T
	Value                T
}

// IConfigurationModel represents a configuration model.
type IConfigurationModel struct {
	Contents  map[string]interface{}
	Keys      []string
	Overrides []IOverrides
}

// IOverrides represents configuration overrides.
type IOverrides struct {
	Keys        []string
	Contents    map[string]interface{}
	Identifiers []string
}

// IConfigurationData represents the configuration data.
type IConfigurationData struct {
	Defaults  IConfigurationModel
	User      IConfigurationModel
	Workspace IConfigurationModel
	Folders   map[string]IConfigurationModel
}

// IConfigurationUpdateOverrides represents configuration update overrides.
type IConfigurationUpdateOverrides struct {
	OverrideIdentifiers []string
	Resource            *basecommon.URI
}

// IConfigurationService is a decorator for the configuration service.
var IConfigurationServiceID = instantiationcommon.CreateDecorator[IConfigurationService]("configurationService")

// ToValuesTree converts a map of properties to a tree of values.
func ToValuesTree(properties map[string]interface{}, conflictReporter func(message string)) map[string]interface{} {
	root := make(map[string]interface{})

	for key, value := range properties {
		AddToValueTree(root, key, value, conflictReporter)
	}

	return root
}

// AddToValueTree adds a value to a tree of values.
func AddToValueTree(settingsTreeRoot map[string]interface{}, key string, value interface{}, conflictReporter func(message string)) {
	segments := strings.Split(key, ".")
	last := segments[len(segments)-1]
	segments = segments[:len(segments)-1]

	curr := settingsTreeRoot
	for i, s := range segments {
		obj, ok := curr[s]
		if !ok {
			obj = make(map[string]interface{})
			curr[s] = obj
		}

		if _, ok := obj.(map[string]interface{}); !ok {
			conflictReporter("Ignoring " + key + " as " + strings.Join(segments[:i+1], ".") + " is " + "not an object")
			return
		}

		curr = obj.(map[string]interface{})
	}

	curr[last] = value
}

// RemoveFromValueTree removes a value from a tree of values.
func RemoveFromValueTree(valueTree map[string]interface{}, key string) {
	segments := strings.Split(key, ".")
	doRemoveFromValueTree(valueTree, segments)
}

func doRemoveFromValueTree(valueTree map[string]interface{}, segments []string) {
	if len(segments) == 0 {
		return
	}

	first := segments[0]
	segments = segments[1:]

	if len(segments) == 0 {
		delete(valueTree, first)
		return
	}

	if value, ok := valueTree[first]; ok {
		if value, ok := value.(map[string]interface{}); ok {
			doRemoveFromValueTree(value, segments)
			if len(value) == 0 {
				delete(valueTree, first)
			}
		}
	}
}

// GetConfigurationValue gets a configuration value from a tree of values.
func GetConfigurationValue[T any](config map[string]interface{}, settingPath string, defaultValue T) T {
	path := strings.Split(settingPath, ".")
	result := accessSetting(config, path)

	if result == nil {
		return defaultValue
	}

	return result.(T)
}

func accessSetting(config map[string]interface{}, path []string) interface{} {
	current := config
	for _, component := range path {
		if value, ok := current[component]; ok {
			if value, ok := value.(map[string]interface{}); ok {
				current = value
			} else {
				return value
			}
		} else {
			return nil
		}
	}
	return current
}

// GetLanguageTagSettingPlainKey extracts the plain key from a language tag setting key
func GetLanguageTagSettingPlainKey(key string) string {
	// Remove language tag brackets like [typescript] from [typescript].editor.tabSize
	if strings.HasPrefix(key, "[") {
		if endIndex := strings.Index(key, "]"); endIndex != -1 {
			return key[endIndex+1:]
		}
	}
	return key
}
