/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/

package common

import (
	"github.com/yudaprama/kawai-agent/vscode_go/vs/base/common"
	backupcommon "github.com/yudaprama/kawai-agent/vscode_go/vs/platform/backup/common"
	"github.com/yudaprama/kawai-agent/vscode_go/vs/platform/workspace/common"
)

// IRecentlyOpened represents the recently opened workspaces and files.
type IRecentlyOpened struct {
	Workspaces []interface{} // IRecentWorkspace or IRecentFolder
	Files      []*IRecentFile
}

// IRecent is a marker interface for recent entries.
type IRecent interface {
	isRecent()
}

// IRecentWorkspace represents a recent workspace.
type IRecentWorkspace struct {
	Workspace       *workspacecommon.IWorkspaceIdentifier
	Label           string
	RemoteAuthority string
}

func (IRecentWorkspace) isRecent() {}

// IRecentFolder represents a recent folder.
type IRecentFolder struct {
	FolderURI       *common.URI
	Label           string
	RemoteAuthority string
}

func (IRecentFolder) isRecent() {}

// IRecentFile represents a recent file.
type IRecentFile struct {
	FileURI         *common.URI
	Label           string
	RemoteAuthority string
}

func (IRecentFile) isRecent() {}

// IsRecentWorkspace checks if a recent entry is a workspace.
func IsRecentWorkspace(recent IRecent) bool {
	_, ok := recent.(*IRecentWorkspace)
	return ok
}

// IsRecentFolder checks if a recent entry is a folder.
func IsRecentFolder(recent IRecent) bool {
	_, ok := recent.(*IRecentFolder)
	return ok
}

// IsRecentFile checks if a recent entry is a file.
func IsRecentFile(recent IRecent) bool {
	_, ok := recent.(*IRecentFile)
	return ok
}

// IWorkspaceFolderCreationData represents data for creating a workspace folder.
type IWorkspaceFolderCreationData struct {
	URI  *common.URI
	Name string
}

// IUntitledWorkspaceInfo represents info for an untitled workspace.
type IUntitledWorkspaceInfo struct {
	Workspace       *workspacecommon.IWorkspaceIdentifier
	RemoteAuthority string
}

// IEnterWorkspaceResult represents the result of entering a workspace.
type IEnterWorkspaceResult struct {
	Workspace  *workspacecommon.IWorkspaceIdentifier
	BackupPath string
}

// IWorkspacesService is the service for managing workspaces.
type IWorkspacesService interface {
	// Workspaces Management
	EnterWorkspace(workspaceURI *common.URI) (*IEnterWorkspaceResult, error)
	CreateUntitledWorkspace(folders []*IWorkspaceFolderCreationData, remoteAuthority string) (*workspacecommon.IWorkspaceIdentifier, error)
	DeleteUntitledWorkspace(workspace *workspacecommon.IWorkspaceIdentifier) error
	GetWorkspaceIdentifier(workspaceURI *common.URI) (*workspacecommon.IWorkspaceIdentifier, error)

	// Workspaces History
	AddRecentlyOpened(recents []IRecent) error
	RemoveRecentlyOpened(workspaces []*common.URI) error
	ClearRecentlyOpened() error
	GetRecentlyOpened() (*IRecentlyOpened, error)

	// Dirty Workspaces
	GetDirtyWorkspaces() ([]interface{}, error) // IWorkspaceBackupInfo or IFolderBackupInfo
}
