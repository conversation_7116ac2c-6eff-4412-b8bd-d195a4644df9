/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/

package electronmain

import (
	"github.com/yudaprama/kawai-agent/vscode_go/vs/base/common"
	"github.com/yudaprama/kawai-agent/vscode_go/vs/base/common/event"
	instantiationcommon "github.com/yudaprama/kawai-agent/vscode_go/vs/platform/instantiation/common"
	workspacescommon "github.com/yudaprama/kawai-agent/vscode_go/vs/platform/workspaces/common"
)

var IWorkspacesHistoryMainServiceID = instantiationcommon.CreateDecorator[IWorkspacesHistoryMainService]("workspacesHistoryMainService")

// ClearRecentlyOpenedOptions provides options for clearing recently opened entries.
type ClearRecentlyOpenedOptions struct {
	Confirm bool
}

// IWorkspacesHistoryMainService is the service for managing the history of recently opened workspaces.
type IWorkspacesHistoryMainService interface {
	OnDidChangeRecentlyOpened() event.Event[interface{}]

	AddRecentlyOpened(recents []workspacescommon.IRecent) error
	GetRecentlyOpened() (*workspacescommon.IRecentlyOpened, error)
	RemoveRecentlyOpened(paths []*common.URI) error
	ClearRecentlyOpened(options ...ClearRecentlyOpenedOptions) error
}
