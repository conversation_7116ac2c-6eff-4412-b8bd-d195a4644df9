/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/

package common

import (
	basecommon "github.com/yudaprama/kawai-agent/vscode_go/vs/base/common"
	nls "github.com/yudaprama/kawai-agent/vscode_go/vs/nls"
	configurationcommon "github.com/yudaprama/kawai-agent/vscode_go/vs/platform/configuration/common"
	"github.com/yudaprama/kawai-agent/vscode_go/vs/platform/registry/common"
)

func init() {
	configurationRegistry := common.Registry.As(configurationcommon.Extensions.Configuration).(configurationcommon.IConfigurationRegistry)

	order := 15
	updateModeDesc := nls.Localize("updateMode", "Configure whether you receive automatic updates. Requires a restart after change. The updates are fetched from a Microsoft online service.")
	updateChannelDesc := nls.Localize("updateChannel", "Configure whether you receive automatic updates. Requires a restart after change. The updates are fetched from a Microsoft online service.")
	showReleaseNotesDesc := nls.Localize("showReleaseNotes", "Show Release Notes after an update. The Release Notes are fetched from a Microsoft online service.")
	enableWindowsBackgroundUpdatesTitle := nls.Localize("enableWindowsBackgroundUpdatesTitle", "Enable Background Updates on Windows")
	enableWindowsBackgroundUpdatesDesc := nls.Localize("enableWindowsBackgroundUpdates", "Enable to download and install new VS Code versions in the background on Windows.")
	included := basecommon.IsWindows && !basecommon.IsWeb
	applicationScope := configurationcommon.ConfigurationScopeApplication

	configurationRegistry.RegisterConfiguration(&configurationcommon.IConfigurationNode{
		ID:    stringPtr("update"),
		Order: &order,
		Title: stringPtr(nls.Localize("updateConfigurationTitle", "Update")),
		Type:  stringPtr("object"),
		Properties: map[string]*configurationcommon.IConfigurationPropertySchema{
			"update.mode": {
				Type:    "string",
				Default: "default",
				Scope:   &applicationScope,
				Description: stringPtr(updateModeDesc + "\n\n" + nls.Localize("none", "Disable updates.") + "\n" + nls.Localize("manual", "Disable automatic background update checks. Updates will be available if you manually check for updates.") + "\n" + nls.Localize("start", "Check for updates only on startup. Disable automatic background update checks.") + "\n" + nls.Localize("default", "Enable automatic update checks. Code will check for updates automatically and periodically.") + "\n\n(uses online services)"),
				Policy: &configurationcommon.IPolicyInfo{
					Name: "UpdateMode",
				},
			},
			"update.channel": {
				Type:        "string",
				Default:     "default",
				Scope:       &applicationScope,
				Description: stringPtr(updateChannelDesc + "\n\n" + nls.Localize("deprecated", "This setting is deprecated, please use '{0}' instead.", "update.mode")),
			},
			"update.enableWindowsBackgroundUpdates": {
				Type:        "boolean",
				Default:     true,
				Scope:       &applicationScope,
				Description: stringPtr(enableWindowsBackgroundUpdatesTitle + "\n\n" + enableWindowsBackgroundUpdatesDesc),
				Included:    &included,
			},
			"update.showReleaseNotes": {
				Type:        "boolean",
				Default:     true,
				Scope:       &applicationScope,
				Description: stringPtr(showReleaseNotesDesc + "\n\n(uses online services)"),
			},
		},
	})
}

func stringPtr(s string) *string {
	return &s
}
