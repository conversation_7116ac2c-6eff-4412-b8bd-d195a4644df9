package common

import (
	"sync"

	basecommon "github.com/yudaprama/kawai-agent/vscode_go/vs/base/common"
)

// IRegistry is the interface for the registry.
type IRegistry interface {
	Add(id string, data interface{})
	Knows(id string) bool
	As(id string) interface{}
}

// RegistryImpl implements IRegistry
type RegistryImpl struct {
	data map[string]interface{}
	mu   sync.RWMutex
}

// NewRegistry creates a new registry
func NewRegistry() *RegistryImpl {
	return &RegistryImpl{
		data: make(map[string]interface{}),
	}
}

// Add adds an extension to the registry
func (r *RegistryImpl) Add(id string, data interface{}) {
	r.mu.Lock()
	defer r.mu.Unlock()

	if !basecommon.IsString(id) {
		panic("id must be a string")
	}
	if !basecommon.IsObject(data) && data != nil {
		panic("data must be an object")
	}
	if r.data[id] != nil {
		panic("There is already an extension with this id")
	}

	r.data[id] = data
}

// Knows returns true if there is an extension with the provided id
func (r *RegistryImpl) Knows(id string) bool {
	r.mu.RLock()
	defer r.mu.RUnlock()
	return r.data[id] != nil
}

// As returns the extension functions and properties defined by the specified key
func (r *RegistryImpl) As(id string) interface{} {
	r.mu.RLock()
	defer r.mu.RUnlock()
	return r.data[id]
}

// Registry is the global registry instance
var Registry IRegistry = NewRegistry()
