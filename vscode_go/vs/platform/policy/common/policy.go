package common

import "github.com/yudaprama/kawai-agent/vscode_go/vs/base/common"

// PolicyName represents a policy name
type PolicyName string

// IPolicy represents a policy interface
type IPolicy struct {
	Name PolicyName `json:"name"`
}

// IPolicyService is the interface for the policy service.
type IPolicyService interface {
	GetPolicyValue(name string) interface{}
	UpdatePolicyDefinitions(definitions map[string]*PolicyDefinition) (interface{}, error)
	OnDidChange() common.Event[[]string]
}

// PolicyDefinition represents a policy definition.
type PolicyDefinition struct {
	Type           string
	PreviewFeature *bool
	DefaultValue   interface{}
}
