/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/

package common

import (
	"regexp"
	"strings"
)

// IPathLabelFormatting represents path label formatting options
type IPathLabelFormatting struct {
	// The OS the path label is from to produce a label
	// that matches OS expectations.
	OS OperatingSystem

	// Whether to add a `~` when the path is in the
	// user home directory.
	//
	// Note: this only applies to Linux, macOS but not
	// Windows.
	Tildify *IUserHomeProvider

	// Whether to convert to a relative path if the path
	// is within any of the opened workspace folders.
	Relative *IRelativePathProvider
}

// IRelativePathProvider represents a relative path provider
type IRelativePathProvider struct {
	// Whether to not add a prefix when in multi-root workspace.
	NoPrefix bool

	GetWorkspace       func() *Workspace
	GetWorkspaceFolder func(resource *URI) *WorkspaceFolder
}

// Workspace represents a workspace with folders
type Workspace struct {
	Folders []*WorkspaceFolder
}

// WorkspaceFolder represents a workspace folder
type WorkspaceFolder struct {
	URI  *URI
	Name string
}

// IUserHomeProvider represents a user home provider
type IUserHomeProvider struct {
	UserHome *URI
}

// GetPathLabel returns a path label for the given resource
func GetPathLabel(resource *URI, formatting *IPathLabelFormatting) string {
	osType := formatting.OS
	tildifier := formatting.Tildify
	relatifier := formatting.Relative

	// return early with a relative path if we can resolve one
	if relatifier != nil {
		relativePath := getRelativePathLabel(resource, relatifier, osType)
		if relativePath != "" {
			return relativePath
		}
	}

	// otherwise try to resolve a absolute path label and
	// apply target OS standard path separators if target
	// OS differs from actual OS we are running in
	absolutePath := resource.FSPath()
	if osType == OSWindows && !isWindows {
		absolutePath = strings.ReplaceAll(absolutePath, "/", "\\")
	} else if osType != OSWindows && isWindows {
		absolutePath = strings.ReplaceAll(absolutePath, "\\", "/")
	}

	// macOS/Linux: tildify with provided user home directory
	if osType != OSWindows && tildifier != nil && tildifier.UserHome != nil {
		userHome := tildifier.UserHome.FSPath()

		// This is a bit of a hack, but in order to figure out if the
		// resource is in the user home, we need to make sure to convert it
		// to a user home resource. We cannot assume that the resource is
		// already a user home resource.
		var userHomeCandidate string
		if resource.Scheme != tildifier.UserHome.Scheme && len(resource.Path) > 0 && resource.Path[0] == posix.Sep()[0] && (len(resource.Path) < 2 || resource.Path[1] != posix.Sep()[0]) {
			userHomeCandidate = tildifier.UserHome.With(UriComponents{Path: resource.Path}).FSPath()
		} else {
			userHomeCandidate = absolutePath
		}

		absolutePath = tildify(userHomeCandidate, userHome, osType)
	}

	// normalize
	var pathLib IPath
	if osType == OSWindows {
		pathLib = win32
	} else {
		pathLib = posix
	}
	return pathLib.Normalize(normalizeDriveLetter(absolutePath, osType == OSWindows))
}

func getRelativePathLabel(resource *URI, relativePathProvider *IRelativePathProvider, osType OperatingSystem) string {
	var pathLib IPath
	var extUriLib IExtUri

	if osType == OSWindows {
		pathLib = win32
	} else {
		pathLib = posix
	}

	if osType == OSLinux {
		extUriLib = extUri
	} else {
		extUriLib = extUriIgnorePathCase
	}

	workspace := relativePathProvider.GetWorkspace()
	if workspace == nil || len(workspace.Folders) == 0 {
		return ""
	}

	firstFolder := workspace.Folders[0]
	if firstFolder == nil {
		return ""
	}

	// This is a bit of a hack, but in order to figure out the folder
	// the resource belongs to, we need to make sure to convert it
	// to a workspace resource. We cannot assume that the resource is
	// already matching the workspace.
	if resource.Scheme != firstFolder.URI.Scheme && len(resource.Path) > 0 && resource.Path[0] == posix.Sep()[0] && (len(resource.Path) < 2 || resource.Path[1] != posix.Sep()[0]) {
		resource = firstFolder.URI.With(UriComponents{Path: resource.Path})
	}

	folder := relativePathProvider.GetWorkspaceFolder(resource)
	if folder == nil {
		return ""
	}

	var relativePathLabel string
	if extUriLib.IsEqual(folder.URI, resource) {
		relativePathLabel = "" // no label if paths are identical
	} else {
		relativePathLabel = extUriLib.RelativePath(folder.URI, resource)
		if relativePathLabel == "" {
			relativePathLabel = ""
		}
	}

	// normalize
	if relativePathLabel != "" {
		relativePathLabel = pathLib.Normalize(relativePathLabel)
	}

	// always show root basename if there are multiple folders
	if len(workspace.Folders) > 1 && !relativePathProvider.NoPrefix {
		var rootName string
		if folder.Name != "" {
			rootName = folder.Name
		} else {
			rootName = extUriLib.BasenameOrAuthority(folder.URI)
		}
		if relativePathLabel != "" {
			relativePathLabel = rootName + " • " + relativePathLabel
		} else {
			relativePathLabel = rootName
		}
	}

	return relativePathLabel
}

func normalizeDriveLetter(path string, isWindowsOS bool) string {
	if HasDriveLetter(path, isWindowsOS) {
		return strings.ToUpper(string(path[0])) + path[1:]
	}
	return path
}

// normalizedUserHomeCached caches normalized user home paths
var normalizedUserHomeCached = struct {
	original   string
	normalized string
}{}

func tildify(path, userHome string, osType OperatingSystem) string {
	if osType == OSWindows || path == "" || userHome == "" {
		return path // unsupported on Windows
	}

	var normalizedUserHome string
	if normalizedUserHomeCached.original == userHome {
		normalizedUserHome = normalizedUserHomeCached.normalized
	} else {
		normalizedUserHome = userHome
		if isWindows {
			normalizedUserHome = ToSlashes(normalizedUserHome) // make sure that the path is POSIX normalized on Windows
		}
		normalizedUserHome = rtrim(normalizedUserHome, posix.Sep()) + posix.Sep()
		normalizedUserHomeCached.original = userHome
		normalizedUserHomeCached.normalized = normalizedUserHome
	}

	normalizedPath := path
	if isWindows {
		normalizedPath = ToSlashes(normalizedPath) // make sure that the path is POSIX normalized on Windows
	}

	// Linux: case sensitive, macOS: case insensitive
	if osType == OSLinux {
		if strings.HasPrefix(normalizedPath, normalizedUserHome) {
			return "~/" + normalizedPath[len(normalizedUserHome):]
		}
	} else {
		if startsWithIgnoreCase(normalizedPath, normalizedUserHome) {
			return "~/" + normalizedPath[len(normalizedUserHome):]
		}
	}

	return path
}

// Untildify replaces the tilde (~) with the user home directory (exported version)
func Untildify(path, userHome string) string {
	re := regexp.MustCompile(`^~($|/|\\)`)
	return re.ReplaceAllString(path, userHome+"$1")
}

// Shortens the paths but keeps them easy to distinguish.
// Replaces not important parts with ellipsis.
// Every shorten path matches only one original path and vice versa.
const (
	ellipsis = "\u2026"
	unc      = "\\\\"
	home     = "~"
)

// Shorten shortens the paths but keeps them easy to distinguish (exported version)
func Shorten(paths []string, pathSeparator ...string) []string {
	separator := sep
	if len(pathSeparator) > 0 {
		separator = pathSeparator[0]
	}

	shortenedPaths := make([]string, len(paths))

	// for every path
	match := false
	for pathIndex := 0; pathIndex < len(paths); pathIndex++ {
		originalPath := paths[pathIndex]

		if originalPath == "" {
			shortenedPaths[pathIndex] = "." + separator
			continue
		}

		if originalPath == "" {
			shortenedPaths[pathIndex] = originalPath
			continue
		}

		match = true

		// trim for now and concatenate unc path (e.g. \\network) or root path (/etc, ~/etc) later
		prefix := ""
		trimmedPath := originalPath
		if strings.Index(trimmedPath, unc) == 0 {
			prefix = trimmedPath[:strings.Index(trimmedPath, unc)+len(unc)]
			trimmedPath = trimmedPath[strings.Index(trimmedPath, unc)+len(unc):]
		} else if strings.Index(trimmedPath, separator) == 0 {
			prefix = trimmedPath[:strings.Index(trimmedPath, separator)+len(separator)]
			trimmedPath = trimmedPath[strings.Index(trimmedPath, separator)+len(separator):]
		} else if strings.Index(trimmedPath, home) == 0 {
			prefix = trimmedPath[:strings.Index(trimmedPath, home)+len(home)]
			trimmedPath = trimmedPath[strings.Index(trimmedPath, home)+len(home):]
		}

		// pick the first shortest subpath found
		segments := strings.Split(trimmedPath, separator)
		for subpathLength := 1; match && subpathLength <= len(segments); subpathLength++ {
			for start := len(segments) - subpathLength; match && start >= 0; start-- {
				match = false
				subpath := strings.Join(segments[start:start+subpathLength], separator)

				// that is unique to any other path
				for otherPathIndex := 0; !match && otherPathIndex < len(paths); otherPathIndex++ {
					// suffix subpath treated specially as we consider no match 'x' and 'x/...'
					if otherPathIndex != pathIndex && paths[otherPathIndex] != "" && strings.Contains(paths[otherPathIndex], subpath) {
						isSubpathEnding := (start+subpathLength == len(segments))

						// Adding separator as prefix for subpath, such that 'endsWith(src, trgt)' considers subpath as directory name instead of plain string.
						// prefix is not added when either subpath is root directory or path[otherPathIndex] does not have multiple directories.
						var subpathWithSep string
						if start > 0 && strings.Index(paths[otherPathIndex], separator) > -1 {
							subpathWithSep = separator + subpath
						} else {
							subpathWithSep = subpath
						}
						isOtherPathEnding := strings.HasSuffix(paths[otherPathIndex], subpathWithSep)

						match = !isSubpathEnding || isOtherPathEnding
					}
				}

				// found unique subpath
				if !match {
					result := ""

					// preserve disk drive or root prefix
					if strings.HasSuffix(segments[0], ":") || prefix != "" {
						if start == 1 {
							// extend subpath to include disk drive prefix
							start = 0
							subpathLength++
							subpath = segments[0] + separator + subpath
						}

						if start > 0 {
							result = segments[0] + separator
						}

						result = prefix + result
					}

					// add ellipsis at the beginning if needed
					if start > 0 {
						result = result + ellipsis + separator
					}

					result = result + subpath

					// add ellipsis at the end if needed
					if start+subpathLength < len(segments) {
						result = result + separator + ellipsis
					}

					shortenedPaths[pathIndex] = result
				}
			}
		}

		if match {
			shortenedPaths[pathIndex] = originalPath // use original path if no unique subpaths found
		}
	}

	return shortenedPaths
}

// ISeparator represents a separator in template processing
type ISeparator struct {
	Label string
}

// Type represents the type of template segment
type Type int

const (
	TEXT Type = iota
	VARIABLE
	SEPARATOR
)

// ISegment represents a template segment
type ISegment struct {
	Value string
	Type  Type
}

// Template processes a template string with variable substitution
func Template(template string, values map[string]any) string {
	if values == nil {
		values = make(map[string]any)
	}

	var segments []ISegment
	inVariable := false
	curVal := ""

	for _, char := range template {
		// Beginning of variable
		if char == '$' || (inVariable && char == '{') {
			if curVal != "" {
				segments = append(segments, ISegment{Value: curVal, Type: TEXT})
			}
			curVal = ""
			inVariable = true
		} else if char == '}' && inVariable {
			// End of variable
			resolved := values[curVal]

			// Variable
			if str, ok := resolved.(string); ok {
				if len(str) > 0 {
					segments = append(segments, ISegment{Value: str, Type: VARIABLE})
				}
			} else if sep, ok := resolved.(*ISeparator); ok {
				// Separator
				prevSegment := ISegment{}
				if len(segments) > 0 {
					prevSegment = segments[len(segments)-1]
				}
				if prevSegment.Type != SEPARATOR {
					segments = append(segments, ISegment{Value: sep.Label, Type: SEPARATOR}) // prevent duplicate separators
				}
			}

			curVal = ""
			inVariable = false
		} else {
			// Text or Variable Name
			curVal += string(char)
		}
	}

	// Tail
	if curVal != "" && !inVariable {
		segments = append(segments, ISegment{Value: curVal, Type: TEXT})
	}

	// Filter segments
	var filteredSegments []ISegment
	for index, segment := range segments {
		// Only keep separator if we have values to the left and right
		if segment.Type == SEPARATOR {
			var left, right ISegment
			if index > 0 {
				left = segments[index-1]
			}
			if index < len(segments)-1 {
				right = segments[index+1]
			}

			leftValid := (left.Type == VARIABLE || left.Type == TEXT) && len(left.Value) > 0
			rightValid := (right.Type == VARIABLE || right.Type == TEXT) && len(right.Value) > 0

			if leftValid && rightValid {
				filteredSegments = append(filteredSegments, segment)
			}
		} else {
			// accept any TEXT and VARIABLE
			filteredSegments = append(filteredSegments, segment)
		}
	}

	// Join segments
	result := ""
	for _, segment := range filteredSegments {
		result += segment.Value
	}

	return result
}

// MnemonicMenuLabel handles mnemonics for menu items. Depending on OS:
// - Windows: Supported via & character (replace && with &)
// -   Linux: Supported via & character (replace && with &)
// -   macOS: Unsupported (replace && with empty string)
func MnemonicMenuLabel(label string, forceDisableMnemonics ...bool) string {
	forceDisable := false
	if len(forceDisableMnemonics) > 0 {
		forceDisable = forceDisableMnemonics[0]
	}

	if isMacintosh || forceDisable {
		// Remove mnemonics for macOS or when forced
		re1 := regexp.MustCompile(`\(&&\w\)|&&`)
		result := re1.ReplaceAllString(label, "")

		if isMacintosh {
			return strings.ReplaceAll(result, "&", "&")
		} else {
			return strings.ReplaceAll(result, "&", "&&")
		}
	}

	// For Windows/Linux
	re := regexp.MustCompile(`&&|&`)
	return re.ReplaceAllStringFunc(label, func(m string) string {
		if m == "&" {
			return "&&"
		}
		return "&"
	})
}

// MnemonicButtonLabelResult represents the result of mnemonic button label processing
type MnemonicButtonLabelResult struct {
	WithMnemonic    string
	WithoutMnemonic string
}

// MnemonicButtonLabel handles mnemonics for buttons. Depending on OS:
// - Windows: Supported via & character (replace && with & and & with && for escaping)
// -   Linux: Supported via _ character (replace && with _)
// -   macOS: Unsupported (replace && with empty string)
func MnemonicButtonLabel(label string, forceDisableMnemonics ...bool) any {
	forceDisable := false
	if len(forceDisableMnemonics) > 0 {
		forceDisable = forceDisableMnemonics[0]
	}

	re := regexp.MustCompile(`\(&&\w\)|&&`)
	withoutMnemonic := re.ReplaceAllString(label, "")

	if forceDisable {
		return withoutMnemonic
	}

	if isMacintosh {
		return MnemonicButtonLabelResult{
			WithMnemonic:    withoutMnemonic,
			WithoutMnemonic: withoutMnemonic,
		}
	}

	var withMnemonic string
	if isWindows {
		re := regexp.MustCompile(`&&|&`)
		withMnemonic = re.ReplaceAllStringFunc(label, func(m string) string {
			if m == "&" {
				return "&&"
			}
			return "&"
		})
	} else {
		withMnemonic = strings.ReplaceAll(label, "&&", "_")
	}

	return MnemonicButtonLabelResult{
		WithMnemonic:    withMnemonic,
		WithoutMnemonic: withoutMnemonic,
	}
}

// UnmnemonicLabel removes mnemonics from a label
func UnmnemonicLabel(label string) string {
	return strings.ReplaceAll(label, "&", "&&")
}

// SplitRecentLabelResult represents the result of splitting a recent label
type SplitRecentLabelResult struct {
	Name       string
	ParentPath string
}

// SplitRecentLabel splits a recent label in name and parent path, supporting both '/' and '\' and workspace suffixes.
// If the location is remote, the remote name is included in the name part.
func SplitRecentLabel(recentLabel string) SplitRecentLabelResult {
	if strings.HasSuffix(recentLabel, "]") {
		// label with workspace suffix
		lastIndexOfSquareBracket := strings.LastIndex(recentLabel[:len(recentLabel)-1], " [")
		if lastIndexOfSquareBracket != -1 {
			split := splitName(recentLabel[:lastIndexOfSquareBracket])
			remoteNameWithSpace := recentLabel[lastIndexOfSquareBracket:]
			return SplitRecentLabelResult{
				Name:       split.Name + remoteNameWithSpace,
				ParentPath: split.ParentPath,
			}
		}
	}
	return splitName(recentLabel)
}

func splitName(fullPath string) SplitRecentLabelResult {
	var pathLib IPath
	if strings.Contains(fullPath, "/") {
		pathLib = posix
	} else {
		pathLib = win32
	}

	name := pathLib.Basename(fullPath)
	parentPath := pathLib.Dirname(fullPath)

	if len(name) > 0 {
		return SplitRecentLabelResult{
			Name:       name,
			ParentPath: parentPath,
		}
	}

	// only the root segment
	return SplitRecentLabelResult{
		Name:       parentPath,
		ParentPath: "",
	}
}
