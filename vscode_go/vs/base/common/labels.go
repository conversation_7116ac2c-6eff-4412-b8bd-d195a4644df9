/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/

package common

import (
	"path"
	"path/filepath"
	"strings"

	"github.com/yudaprama/kawai-agent/vscode_go/vs/base/common/resources"
	"github.com/yudaprama/kawai-agent/vscode_go/vs/base/common/uri"
)

// IPathLabelFormatting defines options for formatting a path label.
type PathLabelFormatting struct {
	// OS is the operating system to use for formatting.
	OS OperatingSystem
	// Tildify specifies a provider for tildifying paths.
	Tildify UserHomeProvider
	// Relative specifies a provider for creating relative paths.
	Relative RelativePathProvider
}

// RelativePathProvider provides workspace context for relative path generation.
type RelativePathProvider interface {
	GetWorkspace() struct {
		Folders []struct {
			URI  *uri.URI
			Name string
		}
	}
	GetWorkspaceFolder(resource *uri.URI) struct {
		URI  *uri.URI
		Name string
	}
	NoPrefix() bool
}

// UserHomeProvider provides the user's home directory URI.
type UserHomeProvider interface {
	UserHome() *uri.URI
}

// GetPathLabel returns a path label for the given resource URI.
func GetPathLabel(resource *uri.URI, formatting PathLabelFormatting) string {
	// Return early with a relative path if we can resolve one
	if formatting.Relative != nil {
		if relativePath, ok := getRelativePathLabel(resource, formatting.Relative, formatting.OS); ok {
			return relativePath
		}
	}

	// Otherwise, try to resolve an absolute path label
	absolutePath := resource.FSPath()
	if formatting.OS == Windows && OS != Windows {
		absolutePath = strings.ReplaceAll(absolutePath, "/", "\\")
	} else if formatting.OS != Windows && OS == Windows {
		absolutePath = strings.ReplaceAll(absolutePath, "\\", "/")
	}

	// Tildify on macOS/Linux
	if formatting.OS != Windows && formatting.Tildify != nil {
		if userHome := formatting.Tildify.UserHome(); userHome != nil {
			absolutePath = Tildify(absolutePath, userHome.FSPath(), formatting.OS)
		}
	}

	// Normalize
	var pathLib pathUtil
	if formatting.OS == Windows {
		pathLib = win32PathUtil{}
	} else {
		pathLib = posixPathUtil{}
	}
	return pathLib.Normalize(NormalizeDriveLetter(absolutePath, formatting.OS == Windows))
}

func getRelativePathLabel(resource *uri.URI, relativePathProvider RelativePathProvider, os OperatingSystem) (string, bool) {
	workspace := relativePathProvider.GetWorkspace()
	if len(workspace.Folders) == 0 {
		return "", false
	}

	folder := relativePathProvider.GetWorkspaceFolder(resource)
	if folder.URI == nil {
		return "", false
	}

	extURI := resources.ExtURI(os == Linux)

	var relativePathLabel string
	if extURI.Equal(folder.URI, resource) {
		relativePathLabel = ""
	} else {
		relativePath, ok := extURI.Relative(folder.URI, resource)
		if !ok {
			return "", false
		}
		relativePathLabel = relativePath
	}

	// Normalize
	if relativePathLabel != "" {
		var pathLib pathUtil
		if os == Windows {
			pathLib = win32PathUtil{}
		} else {
			pathLib = posixPathUtil{}
		}
		relativePathLabel = pathLib.Normalize(relativePathLabel)
	}

	if len(workspace.Folders) > 1 && !relativePathProvider.NoPrefix() {
		rootName := folder.Name
		if rootName == "" {
			rootName = extURI.BasenameOrAuthority(folder.URI)
		}
		if relativePathLabel != "" {
			relativePathLabel = rootName + " • " + relativePathLabel
		} else {
			relativePathLabel = rootName
		}
	}

	return relativePathLabel, true
}

// NormalizeDriveLetter normalizes the drive letter of a path, if it has one.
func NormalizeDriveLetter(path string, isWindowsOS bool) string {
	if HasDriveLetter(path, isWindowsOS) {
		return strings.ToUpper(string(path[0])) + path[1:]
	}
	return path
}

// Tildify replaces the user home directory with a tilde (~).
func Tildify(path, userHome string, os OperatingSystem) string {
	if os == Windows || path == "" || userHome == "" {
		return path
	}

	normalizedUserHome := userHome
	if OS == Windows {
		normalizedUserHome = ToSlashes(normalizedUserHome)
	}
	normalizedUserHome = strings.TrimRight(normalizedUserHome, string(filepath.Separator)) + string(filepath.Separator)

	normalizedPath := path
	if OS == Windows {
		normalizedPath = ToSlashes(normalizedPath)
	}

	if os == Linux {
		if strings.HasPrefix(normalizedPath, normalizedUserHome) {
			return "~/" + normalizedPath[len(normalizedUserHome):]
		}
	} else { // macOS is case-insensitive
		if strings.HasPrefix(strings.ToLower(normalizedPath), strings.ToLower(normalizedUserHome)) {
			return "~/" + normalizedPath[len(normalizedUserHome):]
		}
	}

	return path
}

// Untildify replaces the tilde (~) with the user home directory.
func Untildify(p, userHome string) string {
	if strings.HasPrefix(p, "~") {
		return filepath.Join(userHome, p[1:])
	}
	return p
}

// pathUtil is an interface for path operations to abstract over posix and win32.

type pathUtil interface {
	Normalize(path string) string
}

type posixPathUtil struct{}

func (p posixPathUtil) Normalize(path string) string {
	return path.Clean(path)
}

type win32PathUtil struct{}

func (p win32PathUtil) Normalize(path string) string {
	return filepath.Clean(path)
}
