package common

import (
	"encoding/json"
	"os"
	"runtime"
)

// Platform represents the operating system platform.
type Platform int

const (
	// Web is the web platform.
	Web Platform = iota
	// Mac is the macOS platform.
	Mac
	// Linux is the Linux platform.
	Linux
	// Windows is the Windows platform.
	Windows
)

// PlatformToString returns the string representation of a platform.
func PlatformToString(platform Platform) string {
	switch platform {
	case Web:
		return "Web"
	case Mac:
		return "Mac"
	case Linux:
		return "Linux"
	case Windows:
		return "Windows"
	}
	return ""
}

var (
	// IsWindows is true if the current platform is Windows.
	IsWindows = (runtime.GOOS == "windows")
	// IsMacintosh is true if the current platform is macOS.
	IsMacintosh = (runtime.GOOS == "darwin")
	// IsLinux is true if the current platform is Linux.
	IsLinux = (runtime.GOOS == "linux")
	// IsLinuxSnap is true if the current platform is a Linux Snap build.
	IsLinuxSnap = IsLinux && os.Getenv("SNAP") != "" && os.Getenv("SNAP_REVISION") != ""
	// IsNative is true if the current platform is a native build.
	IsNative = true // Assuming a native Go environment
	// IsElectron is true if the current environment is Electron.
	IsElectron = false // Not applicable in Go
	// IsWeb is true if the current environment is a web browser.
	IsWeb = false // Not applicable in Go
	// IsWebWorker is true if the current environment is a web worker.
	IsWebWorker = false // Not applicable in Go
	// IsIOS is true if the current platform is iOS.
	IsIOS = false // Not applicable in Go
	// IsMobile is true if the current environment is a mobile device.
	IsMobile = false // Not applicable in Go
	// IsCI is true if running in a CI environment.
	IsCI = os.Getenv("CI") != "" || os.Getenv("BUILD_ARTIFACTSTAGINGDIRECTORY") != "" || os.Getenv("GITHUB_WORKSPACE") != ""
)

// PlatformVar is the current platform.
var PlatformVar = func() Platform {
	if IsMacintosh {
		return Mac
	}
	if IsWindows {
		return Windows
	}
	if IsLinux {
		return Linux
	}
	return Web
}()

// OperatingSystem represents the operating system.
type OperatingSystem int

const (
	// OSWindows is the Windows operating system.
	OSWindows OperatingSystem = 1
	// OSMacintosh is the macOS operating system.
	OSMacintosh OperatingSystem = 2
	// OSLinux is the Linux operating system.
	OSLinux OperatingSystem = 3
)

// OS is the current operating system.
var OS = func() OperatingSystem {
	if IsMacintosh || IsIOS {
		return OSMacintosh
	}
	if IsWindows {
		return OSWindows
	}
	return OSLinux
}()

// IProcessEnvironment represents the process environment.
type IProcessEnvironment map[string]string

// Language is the language used for the user interface.
var Language = "en"

// Locale is the locale of the environment.
var Locale = "en"

// PlatformLocale is the locale of the platform.
var PlatformLocale = "en"

// TranslationsConfigFile is the path to the translations config file.
var TranslationsConfigFile string

// NLSConfig represents the structure of the VSCODE_NLS_CONFIG JSON.
	type NLSConfig struct {
		UserLocale      string `json:"userLocale"`
		OSLocale        string `json:"osLocale"`
		ResolvedLanguage string `json:"resolvedLanguage"`
		LanguagePack    struct {
			TranslationsConfigFile string `json:"translationsConfigFile"`
		} `json:"languagePack"`
	}

	func init() {
		rawNlsConfig := os.Getenv("VSCODE_NLS_CONFIG")
		if rawNlsConfig != "" {
			var nlsConfig NLSConfig
			err := json.Unmarshal([]byte(rawNlsConfig), &nlsConfig)
			if err == nil {
				if nlsConfig.UserLocale != "" {
					Locale = nlsConfig.UserLocale
				}
				if nlsConfig.OSLocale != "" {
					PlatformLocale = nlsConfig.OSLocale
				}
				if nlsConfig.ResolvedLanguage != "" {
					Language = nlsConfig.ResolvedLanguage
				}
				if nlsConfig.LanguagePack.TranslationsConfigFile != "" {
					TranslationsConfigFile = nlsConfig.LanguagePack.TranslationsConfigFile
				}
			}
		}
	}
